# 部署指南

## 环境要求

- Docker 20.0+
- Docker Compose 2.0+
- 至少 4GB 内存
- 至少 10GB 磁盘空间

## 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd knowledge-management-platform
```

### 2. 配置环境变量
```bash
cp docker/.env.example docker/.env
# 编辑 docker/.env 文件，配置相关参数
```

### 3. 启动服务

**Linux/macOS:**
```bash
chmod +x scripts/start.sh
./scripts/start.sh
```

**Windows:**
```cmd
scripts\start.bat
```

### 4. 访问系统
- 前端管理界面: http://localhost:3000
- 后端API: http://localhost:5000
- 默认管理员账户: admin / admin123456

## 手动部署

### 1. 启动基础服务
```bash
cd docker
docker-compose up -d mysql redis
```

### 2. 等待数据库启动
```bash
docker-compose logs -f mysql
# 等待看到 "ready for connections" 消息
```

### 3. 启动应用服务
```bash
docker-compose up -d backend frontend nginx
```

### 4. 检查服务状态
```bash
docker-compose ps
```

## 配置说明

### 环境变量配置 (.env)

```bash
# MySQL配置
MYSQL_ROOT_PASSWORD=knowledge123456

# 应用配置
FLASK_ENV=production
SECRET_KEY=your-production-secret-key-here
JWT_SECRET_KEY=your-production-jwt-secret-key-here

# RAGFlow配置
RAGFLOW_BASE_URL=http://**************:180
RAGFLOW_API_KEY=ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm

# 管理员账户配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123456
ADMIN_EMAIL=<EMAIL>
```

### 端口配置

- 3000: 前端界面
- 5000: 后端API
- 3306: MySQL数据库
- 6379: Redis缓存
- 80: Nginx反向代理

## 数据持久化

项目使用Docker卷来持久化数据：

- `mysql_data`: MySQL数据库文件
- `redis_data`: Redis数据文件
- `./uploads`: 上传的文件
- `./logs`: 应用日志

## 日志查看

### 查看所有服务日志
```bash
docker-compose logs -f
```

### 查看特定服务日志
```bash
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mysql
```

### 查看应用日志
```bash
# 后端应用日志
tail -f docker/logs/app.log

# 访问日志
docker-compose logs -f nginx
```

## 备份与恢复

### 数据库备份
```bash
docker-compose exec mysql mysqldump -u root -p knowledge_platform > backup.sql
```

### 数据库恢复
```bash
docker-compose exec -i mysql mysql -u root -p knowledge_platform < backup.sql
```

### 文件备份
```bash
tar -czf uploads_backup.tar.gz docker/uploads/
```

## 故障排除

### 常见问题

1. **端口冲突**
   - 修改 docker-compose.yml 中的端口映射
   - 确保端口未被其他服务占用

2. **数据库连接失败**
   - 检查MySQL容器是否正常启动
   - 验证数据库配置是否正确

3. **RAGFlow连接失败**
   - 检查RAGFlow服务是否可访问
   - 验证API Key是否正确

4. **文件上传失败**
   - 检查uploads目录权限
   - 确认文件大小限制配置

### 重置系统

```bash
# 停止所有服务
docker-compose down

# 清理数据（注意：这会删除所有数据）
docker-compose down -v

# 重新启动
docker-compose up -d
```

## 生产环境建议

1. **安全配置**
   - 修改默认密码
   - 使用HTTPS
   - 配置防火墙

2. **性能优化**
   - 增加worker进程数
   - 配置数据库连接池
   - 启用Redis持久化

3. **监控告警**
   - 配置日志收集
   - 设置健康检查
   - 监控资源使用

4. **备份策略**
   - 定期备份数据库
   - 备份上传文件
   - 配置自动备份脚本
