@echo off
chcp 65001 >nul

echo ========================================
echo   知识库管理平台 - 后端服务启动
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或未添加到PATH
    echo 请安装Python 3.8+并添加到系统PATH
    pause
    exit /b 1
)

REM 显示Python版本
echo 当前Python版本:
python --version

REM 检查虚拟环境
if exist "venv\Scripts\activate.bat" (
    echo.
    echo 激活虚拟环境...
    call venv\Scripts\activate.bat
) else (
    echo.
    echo 警告: 未找到虚拟环境
    echo 建议创建虚拟环境: python -m venv venv
    echo.
)

REM 检查依赖是否安装
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 检测到依赖未安装，正在自动安装...
    python install_deps.py
    if errorlevel 1 (
        echo 依赖安装失败，请手动安装
        pause
        exit /b 1
    )
) else (
    echo 依赖检查通过
)

echo.
echo 正在启动后端服务...
echo 服务地址: http://localhost:5000
echo 按 Ctrl+C 停止服务器
echo.

REM 启动应用
python run.py

pause
