// 敏感词管理页面

class SensitiveWordPage extends BasePage {
    constructor() {
        super()
        this.currentPage = 1
        this.pageSize = 20
        this.searchKeyword = ''
        this.categoryFilter = ''
        this.levelFilter = ''
        this.statusFilter = ''
        this.data = []
        this.pagination = null
        this.categories = []
    }

    async render() {
        const html = `
            <div class="page-header">
                <h1 class="page-title">敏感词管理</h1>
                <p class="page-description">管理系统敏感词库，配置过滤规则</p>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">敏感词列表</h3>
                    <div class="toolbar">
                        <div class="toolbar-left">
                            ${createSearchBox('搜索敏感词...', 'searchSensitiveWords')}
                            <select class="form-control" id="category-filter" style="width: 120px;">
                                <option value="">全部分类</option>
                            </select>
                            <select class="form-control" id="level-filter" style="width: 120px;">
                                <option value="">全部级别</option>
                                <option value="low">低</option>
                                <option value="medium">中</option>
                                <option value="high">高</option>
                            </select>
                            <select class="form-control" id="status-filter" style="width: 120px;">
                                <option value="">全部状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">未激活</option>
                            </select>
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-success" onclick="showBatchCreateModal()">
                                <i class="fas fa-upload"></i> 批量导入
                            </button>
                            <button class="btn btn-primary" onclick="showCreateSensitiveWordModal()">
                                <i class="fas fa-plus"></i> 新建敏感词
                            </button>
                            <button class="btn" onclick="refreshSensitiveWordList()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="sensitive-word-table">
                        <!-- 表格内容将在这里动态加载 -->
                    </div>
                </div>
            </div>
        `
        
        this.renderContent(html)
        await this.loadCategories()
        await this.loadData()
    }

    async loadCategories() {
        try {
            this.categories = await sensitiveWordAPI.getCategories()
            
            // 更新分类筛选器
            const categoryFilter = document.getElementById('category-filter')
            if (categoryFilter) {
                this.categories.forEach(category => {
                    const option = document.createElement('option')
                    option.value = category
                    option.textContent = category
                    categoryFilter.appendChild(option)
                })
            }
        } catch (error) {
            console.error('Failed to load categories:', error)
            this.categories = []
        }
    }

    async loadData() {
        try {
            showLoading()
            
            const params = {
                page: this.currentPage,
                per_page: this.pageSize
            }
            
            if (this.searchKeyword) params.search = this.searchKeyword
            if (this.categoryFilter) params.category = this.categoryFilter
            if (this.levelFilter) params.level = this.levelFilter
            if (this.statusFilter) params.status = this.statusFilter
            
            const result = await sensitiveWordAPI.list(params)
            this.data = result.items
            this.pagination = result.pagination
            
            this.renderTable()
        } catch (error) {
            showMessage(error.message || '加载失败', 'error')
        } finally {
            hideLoading()
        }
    }

    renderTable() {
        const columns = [
            {
                title: '敏感词',
                dataIndex: 'word',
                render: (value) => `<strong>${escapeHtml(value)}</strong>`
            },
            {
                title: '分类',
                dataIndex: 'category',
                width: '100px',
                render: (value) => value ? `<span class="tag primary">${escapeHtml(value)}</span>` : '-'
            },
            {
                title: '级别',
                dataIndex: 'level',
                width: '80px',
                render: (value) => {
                    const levelMap = {
                        'low': { text: '低', class: 'success' },
                        'medium': { text: '中', class: 'warning' },
                        'high': { text: '高', class: 'danger' }
                    }
                    const level = levelMap[value] || { text: value, class: 'primary' }
                    return `<span class="tag ${level.class}">${level.text}</span>`
                }
            },
            {
                title: '替换词',
                dataIndex: 'replacement',
                width: '100px',
                render: (value) => value ? escapeHtml(value) : '-'
            },
            {
                title: '处理动作',
                dataIndex: 'action',
                width: '100px',
                render: (value) => {
                    const actionMap = {
                        'replace': '替换',
                        'block': '阻止',
                        'warn': '警告'
                    }
                    return actionMap[value] || value
                }
            },
            {
                title: '命中次数',
                dataIndex: 'hit_count',
                width: '100px'
            },
            {
                title: '状态',
                dataIndex: 'status',
                width: '80px',
                render: (value) => getStatusTag(value)
            }
        ]

        const actions = [
            {
                text: '<i class="fas fa-edit"></i>',
                className: 'btn btn-sm',
                onClick: 'editSensitiveWord'
            },
            {
                text: '<i class="fas fa-trash"></i>',
                className: 'btn btn-sm btn-danger',
                onClick: 'deleteSensitiveWord'
            }
        ]

        const tableHTML = createDataTable(columns, this.data, { actions })
        const paginationHTML = createPagination(this.pagination, 'changeSensitiveWordPage')
        
        document.getElementById('sensitive-word-table').innerHTML = tableHTML + paginationHTML
    }

    bindEvents() {
        // 绑定搜索事件
        bindSearchEvents((value) => {
            this.searchKeyword = value
            this.currentPage = 1
            this.loadData()
        })

        // 绑定筛选事件
        const filters = ['category-filter', 'level-filter', 'status-filter']
        filters.forEach(filterId => {
            const filter = document.getElementById(filterId)
            if (filter) {
                filter.addEventListener('change', (e) => {
                    const filterType = filterId.replace('-filter', '')
                    this[filterType + 'Filter'] = e.target.value
                    this.currentPage = 1
                    this.loadData()
                })
            }
        })

        // 绑定全局函数
        window.searchSensitiveWords = (keyword) => {
            this.searchKeyword = keyword
            this.currentPage = 1
            this.loadData()
        }

        window.changeSensitiveWordPage = (page) => {
            this.currentPage = page
            this.loadData()
        }

        window.refreshSensitiveWordList = () => {
            this.loadData()
        }

        window.editSensitiveWord = (id) => {
            this.editSensitiveWord(id)
        }

        window.deleteSensitiveWord = (id) => {
            this.deleteSensitiveWord(id)
        }

        window.showCreateSensitiveWordModal = () => {
            this.showCreateModal()
        }

        window.showBatchCreateModal = () => {
            this.showBatchCreateModal()
        }

        window.submitCreateSensitiveWord = (data) => {
            return this.createSensitiveWord(data)
        }

        window.submitUpdateSensitiveWord = (data) => {
            return this.updateSensitiveWord(this.editingId, data)
        }

        window.submitBatchCreateSensitiveWords = (data) => {
            return this.batchCreateSensitiveWords(data)
        }
    }

    // 显示创建模态框
    showCreateModal() {
        const fields = [
            { name: 'word', label: '敏感词', type: 'text', required: true, placeholder: '请输入敏感词' },
            { name: 'category', label: '分类', type: 'text', placeholder: '请输入分类' },
            {
                name: 'level',
                label: '敏感级别',
                type: 'select',
                defaultValue: 'medium',
                options: [
                    { value: 'low', label: '低' },
                    { value: 'medium', label: '中' },
                    { value: 'high', label: '高' }
                ]
            },
            { name: 'replacement', label: '替换词', type: 'text', placeholder: '请输入替换词', defaultValue: '***' },
            {
                name: 'action',
                label: '处理动作',
                type: 'select',
                defaultValue: 'replace',
                options: [
                    { value: 'replace', label: '替换' },
                    { value: 'block', label: '阻止' },
                    { value: 'warn', label: '警告' }
                ]
            }
        ]

        createFormModal('创建敏感词', fields, 'submitCreateSensitiveWord')
    }

    // 批量创建敏感词
    async batchCreateSensitiveWords(data) {
        const words = data.words.split('\n').map(w => w.trim()).filter(w => w)
        const result = await sensitiveWordAPI.batchCreate(words, {
            category: data.category,
            level: data.level
        })
        showMessage(`批量导入完成，成功创建${result.created_count}个，跳过${result.skipped_count}个`, 'success')
        await this.loadData()
        return result
    }

    // 显示批量创建模态框
    showBatchCreateModal() {
        const modalHTML = `
            <div class="modal-overlay">
                <div class="modal" style="width: 600px;">
                    <div class="modal-header">
                        <h3 class="modal-title">批量导入敏感词</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>敏感词列表 *</label>
                            <textarea id="batch-words" class="form-control" rows="10" placeholder="请输入敏感词，每行一个"></textarea>
                            <small style="color: #999;">每行输入一个敏感词</small>
                        </div>
                        <div class="form-group">
                            <label>默认分类</label>
                            <input type="text" id="batch-category" class="form-control" placeholder="请输入分类">
                        </div>
                        <div class="form-group">
                            <label>默认级别</label>
                            <select id="batch-level" class="form-control">
                                <option value="low">低</option>
                                <option value="medium" selected>中</option>
                                <option value="high">高</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button class="btn btn-primary" onclick="submitBatchCreate()">导入</button>
                    </div>
                </div>
            </div>
        `
        
        document.getElementById('modal-container').innerHTML = modalHTML
        
        // 绑定提交事件
        window.submitBatchCreate = async () => {
            const wordsText = document.getElementById('batch-words').value.trim()
            const category = document.getElementById('batch-category').value.trim()
            const level = document.getElementById('batch-level').value
            
            if (!wordsText) {
                showMessage('请输入敏感词', 'warning')
                return
            }
            
            const words = wordsText.split('\n').map(w => w.trim()).filter(w => w)
            
            try {
                showLoading()
                const result = await sensitiveWordAPI.batchCreate(words, { category, level })
                showMessage(`批量导入完成，成功创建${result.created_count}个，跳过${result.skipped_count}个`, 'success')
                document.querySelector('.modal-overlay').remove()
                await this.loadData()
            } catch (error) {
                showMessage(error.message || '批量导入失败', 'error')
            } finally {
                hideLoading()
            }
        }
    }

    // 创建敏感词
    async createSensitiveWord(data) {
        const result = await sensitiveWordAPI.create(data)
        showMessage('敏感词创建成功', 'success')
        await this.loadData()
        return result
    }

    // 编辑敏感词
    async editSensitiveWord(id) {
        try {
            showLoading()
            const word = await sensitiveWordAPI.get(id)
            
            const fields = [
                { name: 'word', label: '敏感词', type: 'text', required: true },
                { name: 'category', label: '分类', type: 'text' },
                { 
                    name: 'level', 
                    label: '敏感级别', 
                    type: 'select',
                    options: [
                        { value: 'low', label: '低' },
                        { value: 'medium', label: '中' },
                        { value: 'high', label: '高' }
                    ]
                },
                { name: 'replacement', label: '替换词', type: 'text' },
                { 
                    name: 'action', 
                    label: '处理动作', 
                    type: 'select',
                    options: [
                        { value: 'replace', label: '替换' },
                        { value: 'block', label: '阻止' },
                        { value: 'warn', label: '警告' }
                    ]
                },
                {
                    name: 'status',
                    label: '状态',
                    type: 'select',
                    options: [
                        { value: 'active', label: '活跃' },
                        { value: 'inactive', label: '未激活' }
                    ]
                }
            ]

            this.editingId = id
            createFormModal('编辑敏感词', fields, 'submitUpdateSensitiveWord', word)
        } catch (error) {
            showMessage(error.message || '获取敏感词信息失败', 'error')
        } finally {
            hideLoading()
        }
    }

    // 更新敏感词
    async updateSensitiveWord(id, data) {
        const result = await sensitiveWordAPI.update(id, data)
        showMessage('敏感词更新成功', 'success')
        await this.loadData()
        return result
    }

    // 删除敏感词
    deleteSensitiveWord(id) {
        const word = this.data.find(item => item.id === id)
        if (!word) return

        showConfirm(
            `确定要删除敏感词"${word.word}"吗？`,
            async () => {
                try {
                    showLoading()
                    await sensitiveWordAPI.delete(id)
                    showMessage('敏感词删除成功', 'success')
                    await this.loadData()
                } catch (error) {
                    showMessage(error.message || '删除失败', 'error')
                } finally {
                    hideLoading()
                }
            }
        )
    }

    async loadData() {
        try {
            showLoading()
            
            const params = {
                page: this.currentPage,
                per_page: this.pageSize
            }
            
            if (this.searchKeyword) params.search = this.searchKeyword
            if (this.categoryFilter) params.category = this.categoryFilter
            if (this.levelFilter) params.level = this.levelFilter
            if (this.statusFilter) params.status = this.statusFilter
            
            const result = await sensitiveWordAPI.list(params)
            this.data = result.items
            this.pagination = result.pagination
            
            this.renderTable()
        } catch (error) {
            showMessage(error.message || '加载失败', 'error')
        } finally {
            hideLoading()
        }
    }

    renderTable() {
        const columns = [
            {
                title: '敏感词',
                dataIndex: 'word',
                render: (value) => `<strong>${escapeHtml(value)}</strong>`
            },
            {
                title: '分类',
                dataIndex: 'category',
                width: '100px',
                render: (value) => value ? `<span class="tag primary">${escapeHtml(value)}</span>` : '-'
            },
            {
                title: '级别',
                dataIndex: 'level',
                width: '80px',
                render: (value) => {
                    const levelMap = {
                        'low': { text: '低', class: 'success' },
                        'medium': { text: '中', class: 'warning' },
                        'high': { text: '高', class: 'danger' }
                    }
                    const level = levelMap[value] || { text: value, class: 'primary' }
                    return `<span class="tag ${level.class}">${level.text}</span>`
                }
            },
            {
                title: '替换词',
                dataIndex: 'replacement',
                width: '100px',
                render: (value) => value ? escapeHtml(value) : '-'
            },
            {
                title: '处理动作',
                dataIndex: 'action',
                width: '100px',
                render: (value) => {
                    const actionMap = {
                        'replace': '替换',
                        'block': '阻止',
                        'warn': '警告'
                    }
                    return actionMap[value] || value
                }
            },
            {
                title: '命中次数',
                dataIndex: 'hit_count',
                width: '100px'
            },
            {
                title: '状态',
                dataIndex: 'status',
                width: '80px',
                render: (value) => getStatusTag(value)
            }
        ]

        const actions = [
            {
                text: '<i class="fas fa-edit"></i>',
                className: 'btn btn-sm',
                onClick: 'editSensitiveWord'
            },
            {
                text: '<i class="fas fa-trash"></i>',
                className: 'btn btn-sm btn-danger',
                onClick: 'deleteSensitiveWord'
            }
        ]

        const tableHTML = createDataTable(columns, this.data, { actions })
        const paginationHTML = createPagination(this.pagination, 'changeSensitiveWordPage')
        
        document.getElementById('sensitive-word-table').innerHTML = tableHTML + paginationHTML
    }

    bindEvents() {
        // 绑定搜索事件
        bindSearchEvents((value) => {
            this.searchKeyword = value
            this.currentPage = 1
            this.loadData()
        })

        // 绑定筛选事件
        const filters = ['category-filter', 'level-filter', 'status-filter']
        filters.forEach(filterId => {
            const filter = document.getElementById(filterId)
            if (filter) {
                filter.addEventListener('change', (e) => {
                    const filterType = filterId.replace('-filter', '')
                    this[filterType + 'Filter'] = e.target.value
                    this.currentPage = 1
                    this.loadData()
                })
            }
        })

        // 绑定全局函数
        window.searchSensitiveWords = (keyword) => {
            this.searchKeyword = keyword
            this.currentPage = 1
            this.loadData()
        }

        window.changeSensitiveWordPage = (page) => {
            this.currentPage = page
            this.loadData()
        }

        window.refreshSensitiveWordList = () => {
            this.loadData()
        }

        window.editSensitiveWord = (id) => {
            this.editSensitiveWord(id)
        }

        window.deleteSensitiveWord = (id) => {
            this.deleteSensitiveWord(id)
        }

        window.showCreateSensitiveWordModal = () => {
            this.showCreateModal()
        }

        window.showBatchCreateModal = () => {
            this.showBatchCreateModal()
        }

        window.submitCreateSensitiveWord = (data) => {
            return this.createSensitiveWord(data)
        }

        window.submitUpdateSensitiveWord = (data) => {
            return this.updateSensitiveWord(this.editingId, data)
        }
    }

    // 显示创建模态框
    showCreateModal() {
        const fields = [
            { name: 'word', label: '敏感词', type: 'text', required: true, placeholder: '请输入敏感词' },
            { name: 'category', label: '分类', type: 'text', placeholder: '请输入分类' },
            { 
                name: 'level', 
                label: '敏感级别', 
                type: 'select',
                defaultValue: 'medium',
                options: [
                    { value: 'low', label: '低' },
                    { value: 'medium', label: '中' },
                    { value: 'high', label: '高' }
                ]
            },
            { name: 'replacement', label: '替换词', type: 'text', placeholder: '请输入替换词', defaultValue: '***' },
            { 
                name: 'action', 
                label: '处理动作', 
                type: 'select',
                defaultValue: 'replace',
                options: [
                    { value: 'replace', label: '替换' },
                    { value: 'block', label: '阻止' },
                    { value: 'warn', label: '警告' }
                ]
            }
        ]

        createFormModal('创建敏感词', fields, 'submitCreateSensitiveWord')
    }
}
