import requests
import json
from typing import Dict, List, Optional, Any
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class RAGFlowClient:
    """RAGFlow API客户端"""
    
    def __init__(self, base_url: str = None, api_key: str = None):
        self.base_url = base_url or current_app.config['RAGFLOW_BASE_URL']
        self.api_key = api_key or current_app.config['RAGFLOW_API_KEY']
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        })
    
    def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url.rstrip('/')}/api/v1/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            
            result = response.json()
            if result.get('retcode') != 0:
                raise Exception(f"RAGFlow API Error: {result.get('retmsg', 'Unknown error')}")
            
            return result.get('data', {})
            
        except requests.exceptions.RequestException as e:
            logger.error(f"RAGFlow API request failed: {e}")
            raise Exception(f"RAGFlow API request failed: {str(e)}")
    
    # 知识库管理
    def create_dataset(self, name: str, description: str = "", **kwargs) -> Dict[str, Any]:
        """创建知识库"""
        data = {
            'name': name,
            'description': description,
            **kwargs
        }
        return self._request('POST', 'datasets', json=data)
    
    def list_datasets(self, name: str = None, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取知识库列表"""
        params = {'page': page, 'page_size': page_size}
        if name:
            params['name'] = name
        return self._request('GET', 'datasets', params=params)
    
    def get_dataset(self, dataset_id: str) -> Dict[str, Any]:
        """获取知识库详情"""
        return self._request('GET', f'datasets/{dataset_id}')
    
    def update_dataset(self, dataset_id: str, **kwargs) -> Dict[str, Any]:
        """更新知识库"""
        return self._request('PUT', f'datasets/{dataset_id}', json=kwargs)
    
    def delete_dataset(self, dataset_id: str) -> Dict[str, Any]:
        """删除知识库"""
        return self._request('DELETE', f'datasets/{dataset_id}')
    
    # 文档管理
    def upload_document(self, dataset_id: str, file_path: str, **kwargs) -> Dict[str, Any]:
        """上传文档"""
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {'dataset_id': dataset_id, **kwargs}
            
            # 临时移除Content-Type头，让requests自动设置
            headers = self.session.headers.copy()
            headers.pop('Content-Type', None)
            
            url = f"{self.base_url.rstrip('/')}/api/v1/documents"
            response = self.session.post(url, files=files, data=data, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            if result.get('retcode') != 0:
                raise Exception(f"RAGFlow API Error: {result.get('retmsg', 'Unknown error')}")
            
            return result.get('data', {})
    
    def list_documents(self, dataset_id: str, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取文档列表"""
        params = {'dataset_id': dataset_id, 'page': page, 'page_size': page_size}
        return self._request('GET', 'documents', params=params)
    
    def get_document(self, document_id: str) -> Dict[str, Any]:
        """获取文档详情"""
        return self._request('GET', f'documents/{document_id}')
    
    def delete_document(self, document_id: str) -> Dict[str, Any]:
        """删除文档"""
        return self._request('DELETE', f'documents/{document_id}')
    
    def parse_document(self, document_id: str) -> Dict[str, Any]:
        """解析文档"""
        return self._request('POST', f'documents/{document_id}/parse')
    
    def get_document_status(self, document_id: str) -> Dict[str, Any]:
        """获取文档解析状态"""
        return self._request('GET', f'documents/{document_id}/status')
    
    # 聊天助手管理
    def create_chat(self, name: str, dataset_ids: List[str] = None, **kwargs) -> Dict[str, Any]:
        """创建聊天助手"""
        data = {
            'name': name,
            'dataset_ids': dataset_ids or [],
            **kwargs
        }
        return self._request('POST', 'chats', json=data)
    
    def list_chats(self, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取聊天助手列表"""
        params = {'page': page, 'page_size': page_size}
        return self._request('GET', 'chats', params=params)
    
    def get_chat(self, chat_id: str) -> Dict[str, Any]:
        """获取聊天助手详情"""
        return self._request('GET', f'chats/{chat_id}')
    
    def update_chat(self, chat_id: str, **kwargs) -> Dict[str, Any]:
        """更新聊天助手"""
        return self._request('PUT', f'chats/{chat_id}', json=kwargs)
    
    def delete_chat(self, chat_id: str) -> Dict[str, Any]:
        """删除聊天助手"""
        return self._request('DELETE', f'chats/{chat_id}')
    
    # 对话管理
    def create_conversation(self, chat_id: str, name: str = "") -> Dict[str, Any]:
        """创建对话"""
        data = {'chat_id': chat_id, 'name': name}
        return self._request('POST', 'conversations', json=data)
    
    def send_message(self, conversation_id: str, message: str, stream: bool = False) -> Dict[str, Any]:
        """发送消息"""
        data = {
            'conversation_id': conversation_id,
            'message': message,
            'stream': stream
        }
        return self._request('POST', 'conversations/completion', json=data)
    
    def get_conversation_history(self, conversation_id: str) -> Dict[str, Any]:
        """获取对话历史"""
        return self._request('GET', f'conversations/{conversation_id}/messages')

# 全局客户端实例
ragflow_client = RAGFlowClient()
