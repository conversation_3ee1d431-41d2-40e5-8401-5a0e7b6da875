// 聊天助手管理页面

class ChatAssistantPage extends BasePage {
    constructor() {
        super()
        this.currentPage = 1
        this.pageSize = 20
        this.searchKeyword = ''
        this.statusFilter = ''
        this.data = []
        this.pagination = null
        this.knowledgeBases = []
    }

    async render() {
        const html = `
            <div class="page-header">
                <h1 class="page-title">聊天助手</h1>
                <p class="page-description">管理您的AI聊天助手，配置对话参数和关联知识库</p>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">助手列表</h3>
                    <div class="toolbar">
                        <div class="toolbar-left">
                            ${createSearchBox('搜索助手...', 'searchChatAssistants')}
                            <select class="form-control" id="status-filter" style="width: 120px;">
                                <option value="">全部状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">未激活</option>
                            </select>
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-primary" onclick="showCreateChatAssistantModal()">
                                <i class="fas fa-plus"></i> 新建助手
                            </button>
                            <button class="btn" onclick="refreshChatAssistantList()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="chat-assistant-table">
                        <!-- 表格内容将在这里动态加载 -->
                    </div>
                </div>
            </div>
        `
        
        this.renderContent(html)
        await this.loadKnowledgeBases()
        await this.loadData()
    }

    async loadKnowledgeBases() {
        try {
            const result = await knowledgeBaseAPI.list({ per_page: 100 })
            this.knowledgeBases = result.items
        } catch (error) {
            console.error('Failed to load knowledge bases:', error)
            this.knowledgeBases = []
        }
    }

    async loadData() {
        try {
            showLoading()
            
            const params = {
                page: this.currentPage,
                per_page: this.pageSize
            }
            
            if (this.searchKeyword) {
                params.search = this.searchKeyword
            }
            
            if (this.statusFilter) {
                params.status = this.statusFilter
            }
            
            const result = await chatAssistantAPI.list(params)
            this.data = result.items
            this.pagination = result.pagination
            
            this.renderTable()
        } catch (error) {
            showMessage(error.message || '加载失败', 'error')
        } finally {
            hideLoading()
        }
    }

    renderTable() {
        const columns = [
            {
                title: '助手名称',
                dataIndex: 'name',
                render: (value, row) => `
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: #1890ff; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div>
                            <strong>${escapeHtml(value)}</strong>
                            ${row.description ? `<br><small style="color: #999;">${escapeHtml(row.description)}</small>` : ''}
                        </div>
                    </div>
                `
            },
            {
                title: '关联知识库',
                dataIndex: 'knowledge_base_ids',
                width: '150px',
                render: (value) => {
                    if (!value || value.length === 0) return '-'
                    return `<span class="tag primary">${value.length} 个知识库</span>`
                }
            },
            {
                title: '对话数量',
                dataIndex: 'conversation_count',
                width: '100px'
            },
            {
                title: '状态',
                dataIndex: 'status',
                width: '80px',
                render: (value) => getStatusTag(value)
            },
            {
                title: '创建时间',
                dataIndex: 'created_at',
                width: '160px',
                render: (value) => formatDate(value, 'YYYY-MM-DD HH:mm')
            }
        ]

        const actions = [
            {
                text: '<i class="fas fa-comments"></i>',
                className: 'btn btn-sm btn-success',
                onClick: 'testChatAssistant'
            },
            {
                text: '<i class="fas fa-eye"></i>',
                className: 'btn btn-sm',
                onClick: 'viewChatAssistant'
            },
            {
                text: '<i class="fas fa-edit"></i>',
                className: 'btn btn-sm',
                onClick: 'editChatAssistant'
            },
            {
                text: '<i class="fas fa-trash"></i>',
                className: 'btn btn-sm btn-danger',
                onClick: 'deleteChatAssistant'
            }
        ]

        const tableHTML = createDataTable(columns, this.data, { actions })
        const paginationHTML = createPagination(this.pagination, 'changeChatAssistantPage')
        
        document.getElementById('chat-assistant-table').innerHTML = tableHTML + paginationHTML
    }

    bindEvents() {
        // 绑定搜索事件
        bindSearchEvents((value) => {
            this.searchKeyword = value
            this.currentPage = 1
            this.loadData()
        })

        // 绑定状态筛选事件
        const statusFilter = document.getElementById('status-filter')
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.statusFilter = e.target.value
                this.currentPage = 1
                this.loadData()
            })
        }

        // 绑定全局函数
        window.searchChatAssistants = (keyword) => {
            this.searchKeyword = keyword
            this.currentPage = 1
            this.loadData()
        }

        window.changeChatAssistantPage = (page) => {
            this.currentPage = page
            this.loadData()
        }

        window.refreshChatAssistantList = () => {
            this.loadData()
        }

        window.viewChatAssistant = (id) => {
            this.viewChatAssistant(id)
        }

        window.editChatAssistant = (id) => {
            this.editChatAssistant(id)
        }

        window.testChatAssistant = (id) => {
            this.testChatAssistant(id)
        }

        window.deleteChatAssistant = (id) => {
            this.deleteChatAssistant(id)
        }

        window.showCreateChatAssistantModal = () => {
            this.showCreateModal()
        }

        window.submitCreateChatAssistant = (data) => {
            return this.createChatAssistant(data)
        }

        window.submitUpdateChatAssistant = (data) => {
            return this.updateChatAssistant(this.editingId, data)
        }
    }

    // 显示创建模态框
    showCreateModal() {
        const kbOptions = this.knowledgeBases.map(kb => ({
            value: kb.id,
            label: kb.name
        }))

        const fields = [
            { name: 'name', label: '助手名称', type: 'text', required: true, placeholder: '请输入助手名称' },
            { name: 'description', label: '描述', type: 'textarea', placeholder: '请输入助手描述' },
            { name: 'prompt', label: '系统提示词', type: 'textarea', placeholder: '请输入系统提示词' }
        ]

        createFormModal('创建聊天助手', fields, 'submitCreateChatAssistant')
    }

    // 创建聊天助手
    async createChatAssistant(data) {
        const result = await chatAssistantAPI.create(data)
        showMessage('聊天助手创建成功', 'success')
        await this.loadData()
        return result
    }

    // 测试聊天助手
    async testChatAssistant(id) {
        const assistant = this.data.find(item => item.id === id)
        if (!assistant) return

        const modalHTML = `
            <div class="modal-overlay">
                <div class="modal" style="width: 700px; height: 600px;">
                    <div class="modal-header">
                        <h3 class="modal-title">测试助手 - ${escapeHtml(assistant.name)}</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body" style="display: flex; flex-direction: column;">
                        <div id="chat-messages" style="flex: 1; border: 1px solid #d9d9d9; border-radius: 6px; padding: 16px; overflow-y: auto; margin-bottom: 16px; background: #fafafa;">
                            <div style="text-align: center; color: #999; padding: 20px;">
                                开始与助手对话...
                            </div>
                        </div>
                        <div style="display: flex; gap: 12px;">
                            <input type="text" id="test-message" class="form-control" placeholder="输入消息..." style="flex: 1;">
                            <button class="btn btn-primary" onclick="sendTestMessage('${id}')">
                                <i class="fas fa-paper-plane"></i> 发送
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `
        
        document.getElementById('modal-container').innerHTML = modalHTML
        
        // 绑定回车发送
        document.getElementById('test-message').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                window.sendTestMessage(id)
            }
        })
    }

    // 查看助手详情
    async viewChatAssistant(id) {
        try {
            showLoading()
            const assistant = await chatAssistantAPI.get(id)
            const stats = await chatAssistantAPI.getStatistics(id)
            
            const kbNames = assistant.knowledge_base_ids ? 
                assistant.knowledge_base_ids.map(kbId => {
                    const kb = this.knowledgeBases.find(k => k.id === kbId)
                    return kb ? kb.name : kbId
                }).join(', ') : '无'
            
            const modalHTML = `
                <div class="modal-overlay">
                    <div class="modal" style="width: 800px;">
                        <div class="modal-header">
                            <h3 class="modal-title">助手详情 - ${escapeHtml(assistant.name)}</h3>
                            <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="stats-grid" style="grid-template-columns: repeat(3, 1fr); margin-bottom: 20px;">
                                ${createStatCard('对话数量', stats.total_conversations || 0, 'fas fa-comments', 'primary')}
                                ${createStatCard('消息数量', stats.total_messages || 0, 'fas fa-envelope', 'success')}
                                ${createStatCard('知识库数量', stats.knowledge_bases || 0, 'fas fa-database', 'warning')}
                            </div>
                            
                            <div class="form-group">
                                <label>助手ID</label>
                                <input type="text" class="form-control" value="${assistant.id}" readonly>
                            </div>
                            <div class="form-group">
                                <label>RAGFlow ID</label>
                                <input type="text" class="form-control" value="${assistant.ragflow_assistant_id || '-'}" readonly>
                            </div>
                            <div class="form-group">
                                <label>描述</label>
                                <textarea class="form-control" readonly>${assistant.description || ''}</textarea>
                            </div>
                            <div class="form-group">
                                <label>关联知识库</label>
                                <input type="text" class="form-control" value="${kbNames}" readonly>
                            </div>
                            <div class="form-group">
                                <label>系统提示词</label>
                                <textarea class="form-control" readonly style="height: 100px;">${assistant.prompt || ''}</textarea>
                            </div>
                            <div class="form-group">
                                <label>创建时间</label>
                                <input type="text" class="form-control" value="${formatDate(assistant.created_at)}" readonly>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn" onclick="this.closest('.modal-overlay').remove()">关闭</button>
                            <button class="btn btn-success" onclick="this.closest('.modal-overlay').remove(); testChatAssistant('${id}')">测试对话</button>
                            <button class="btn btn-primary" onclick="this.closest('.modal-overlay').remove(); editChatAssistant('${id}')">编辑</button>
                        </div>
                    </div>
                </div>
            `
            
            document.getElementById('modal-container').innerHTML = modalHTML
        } catch (error) {
            showMessage(error.message || '获取详情失败', 'error')
        } finally {
            hideLoading()
        }
    }

    // 编辑聊天助手
    async editChatAssistant(id) {
        try {
            showLoading()
            const assistant = await chatAssistantAPI.get(id)
            
            const fields = [
                { name: 'name', label: '助手名称', type: 'text', required: true },
                { name: 'description', label: '描述', type: 'textarea' },
                { name: 'prompt', label: '系统提示词', type: 'textarea', placeholder: '请输入系统提示词' },
                {
                    name: 'status',
                    label: '状态',
                    type: 'select',
                    options: [
                        { value: 'active', label: '活跃' },
                        { value: 'inactive', label: '未激活' }
                    ]
                }
            ]

            this.editingId = id
            createFormModal('编辑聊天助手', fields, 'submitUpdateChatAssistant', assistant)
        } catch (error) {
            showMessage(error.message || '获取助手信息失败', 'error')
        } finally {
            hideLoading()
        }
    }

    // 创建聊天助手
    async createChatAssistant(data) {
        const result = await chatAssistantAPI.create(data)
        showMessage('聊天助手创建成功', 'success')
        await this.loadData()
        return result
    }

    // 更新聊天助手
    async updateChatAssistant(id, data) {
        const result = await chatAssistantAPI.update(id, data)
        showMessage('聊天助手更新成功', 'success')
        await this.loadData()
        return result
    }

    // 删除聊天助手
    deleteChatAssistant(id) {
        const assistant = this.data.find(item => item.id === id)
        if (!assistant) return

        showConfirm(
            `确定要删除聊天助手"${assistant.name}"吗？此操作不可恢复。`,
            async () => {
                try {
                    showLoading()
                    await chatAssistantAPI.delete(id)
                    showMessage('聊天助手删除成功', 'success')
                    await this.loadData()
                } catch (error) {
                    showMessage(error.message || '删除失败', 'error')
                } finally {
                    hideLoading()
                }
            }
        )
    }

    bindEvents() {
        // 绑定搜索事件
        bindSearchEvents((value) => {
            this.searchKeyword = value
            this.currentPage = 1
            this.loadData()
        })

        // 绑定状态筛选事件
        const statusFilter = document.getElementById('status-filter')
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.statusFilter = e.target.value
                this.currentPage = 1
                this.loadData()
            })
        }

        // 绑定全局函数
        window.searchChatAssistants = (keyword) => {
            this.searchKeyword = keyword
            this.currentPage = 1
            this.loadData()
        }

        window.changeChatAssistantPage = (page) => {
            this.currentPage = page
            this.loadData()
        }

        window.refreshChatAssistantList = () => {
            this.loadData()
        }

        window.viewChatAssistant = (id) => {
            this.viewChatAssistant(id)
        }

        window.editChatAssistant = (id) => {
            this.editChatAssistant(id)
        }

        window.testChatAssistant = (id) => {
            this.testChatAssistant(id)
        }

        window.deleteChatAssistant = (id) => {
            this.deleteChatAssistant(id)
        }

        window.showCreateChatAssistantModal = () => {
            this.showCreateModal()
        }

        window.submitCreateChatAssistant = (data) => {
            return this.createChatAssistant(data)
        }

        window.submitUpdateChatAssistant = (data) => {
            return this.updateChatAssistant(this.editingId, data)
        }

        // 测试消息发送
        window.sendTestMessage = async (assistantId) => {
            const messageInput = document.getElementById('test-message')
            const message = messageInput.value.trim()
            
            if (!message) {
                showMessage('请输入消息', 'warning')
                return
            }

            try {
                const chatMessages = document.getElementById('chat-messages')
                
                // 显示用户消息
                const userMsgHTML = `
                    <div style="margin-bottom: 16px; text-align: right;">
                        <div style="display: inline-block; background: #1890ff; color: white; padding: 8px 12px; border-radius: 12px; max-width: 70%;">
                            ${escapeHtml(message)}
                        </div>
                    </div>
                `
                chatMessages.innerHTML += userMsgHTML
                
                // 显示加载状态
                const loadingHTML = `
                    <div id="loading-message" style="margin-bottom: 16px;">
                        <div style="display: inline-block; background: #f0f0f0; padding: 8px 12px; border-radius: 12px;">
                            <i class="fas fa-spinner fa-spin"></i> 思考中...
                        </div>
                    </div>
                `
                chatMessages.innerHTML += loadingHTML
                chatMessages.scrollTop = chatMessages.scrollHeight
                
                // 清空输入框
                messageInput.value = ''
                
                // 发送消息
                const result = await chatAssistantAPI.test(assistantId, message)
                
                // 移除加载状态
                document.getElementById('loading-message').remove()
                
                // 显示助手回复
                const assistantMsgHTML = `
                    <div style="margin-bottom: 16px;">
                        <div style="display: inline-block; background: white; border: 1px solid #d9d9d9; padding: 8px 12px; border-radius: 12px; max-width: 70%;">
                            ${escapeHtml(result.response)}
                        </div>
                    </div>
                `
                chatMessages.innerHTML += assistantMsgHTML
                chatMessages.scrollTop = chatMessages.scrollHeight
                
            } catch (error) {
                // 移除加载状态
                const loadingMsg = document.getElementById('loading-message')
                if (loadingMsg) loadingMsg.remove()
                
                showMessage(error.message || '发送失败', 'error')
            }
        }
    }

    // 显示创建模态框
    showCreateModal() {
        const fields = [
            { name: 'name', label: '助手名称', type: 'text', required: true, placeholder: '请输入助手名称' },
            { name: 'description', label: '描述', type: 'textarea', placeholder: '请输入助手描述' },
            { name: 'prompt', label: '系统提示词', type: 'textarea', placeholder: '请输入系统提示词，用于指导助手的行为和回答风格' }
        ]

        createFormModal('创建聊天助手', fields, 'submitCreateChatAssistant')
    }
}
