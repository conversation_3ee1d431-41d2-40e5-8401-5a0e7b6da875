include:
  - ./docker-compose-base.yml

services:
  ragflow:
    depends_on:
      mysql:
        condition: service_healthy
    image: ${RAGFLOW_IMAGE}
    container_name: ragflowplus-server
    ports:
      - ${SVR_HTTP_PORT}:9380
      - 80:80
      - 443:443
    volumes:
      - ./ragflow-logs:/ragflow/logs
      - ./nginx/ragflow.conf:/etc/nginx/conf.d/ragflow.conf
      - ./nginx/proxy.conf:/etc/nginx/proxy.conf
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    env_file: .env
    environment:
      - TZ=${TIMEZONE}
      - HF_ENDPOINT=${HF_ENDPOINT}
      - MACOS=${MACOS}
    networks:
      - ragflow
    restart: on-failure
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 后台信息管理系统前端
  management-frontend:
    container_name: ragflowplus-management-frontend
    image: ${RAGFLOWPLUS_MANAGEMENT_WEB_IMAGE}
    volumes:
      - ./nginx/management_nginx.conf:/etc/nginx/conf.d/default.conf
    ports:
      - "8888:80"
    depends_on:
      - management-backend
    env_file: .env
    environment:
      - API_BASE_URL=/api
      - TZ=${TIMEZONE}
    networks:
      - ragflow
    restart: on-failure
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 后台信息管理系统后端
  management-backend:
    container_name: ragflowplus-management-backend
    image: ${RAGFLOWPLUS_MANAGEMENT_SERVER_IMAGE}
    ports:
      - "5000:5000"
    volumes:
      - ./ragflow-plus-logs:/app/logs
      - ./magic-pdf.json:/root/magic-pdf.json

    depends_on:
      mysql:
        condition: service_healthy
      es01:
        condition: service_healthy 
    env_file: .env
    environment:
      - FLASK_ENV=development
      - CORS_ALLOWED_ORIGINS=http://management-frontend
      - MANAGEMENT_ADMIN_USERNAME=${MANAGEMENT_ADMIN_USERNAME:-admin}
      - MANAGEMENT_ADMIN_PASSWORD=${MANAGEMENT_ADMIN_PASSWORD:-12345678}
      - MANAGEMENT_JWT_SECRET=${MANAGEMENT_JWT_SECRET:-12345678}
      - TZ=${TIMEZONE}
    networks:
      - ragflow
    restart: on-failure
    extra_hosts:
      - "host.docker.internal:host-gateway"