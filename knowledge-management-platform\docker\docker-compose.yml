version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: knowledge-platform-backend
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=mysql+pymysql://root:${MYSQL_ROOT_PASSWORD}@mysql:3306/knowledge_platform
      - REDIS_URL=redis://redis:6379/0
      - RAGFLOW_BASE_URL=http://117.72.181.138:180
      - RAGFLOW_API_KEY=ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm
    depends_on:
      - mysql
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped

  # 前端服务
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/Dockerfile.frontend
    container_name: knowledge-platform-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: knowledge-platform-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-knowledge123456}
      - MYSQL_DATABASE=knowledge_platform
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: knowledge-platform-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: knowledge-platform-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  default:
    name: knowledge-platform-network
