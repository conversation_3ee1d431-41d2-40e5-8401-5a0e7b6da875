// 通用组件

// 创建数据表格
function createDataTable(columns, data, options = {}) {
    const {
        actions = [],
        emptyText = '暂无数据',
        className = 'data-table'
    } = options

    let html = `<table class="${className}">`
    
    // 表头
    html += '<thead><tr>'
    columns.forEach(col => {
        html += `<th style="width: ${col.width || 'auto'}">${col.title}</th>`
    })
    if (actions.length > 0) {
        html += '<th style="width: 120px">操作</th>'
    }
    html += '</tr></thead>'
    
    // 表体
    html += '<tbody>'
    if (data.length === 0) {
        const colSpan = columns.length + (actions.length > 0 ? 1 : 0)
        html += `<tr><td colspan="${colSpan}" style="text-align: center; padding: 40px; color: #999;">${emptyText}</td></tr>`
    } else {
        data.forEach(row => {
            html += '<tr>'
            columns.forEach(col => {
                let value = row[col.dataIndex]
                if (col.render) {
                    value = col.render(value, row)
                } else if (value === null || value === undefined) {
                    value = '-'
                }
                html += `<td>${value}</td>`
            })
            
            if (actions.length > 0) {
                html += '<td><div class="action-buttons">'
                actions.forEach(action => {
                    const disabled = action.disabled && action.disabled(row) ? 'disabled' : ''
                    const className = action.className || 'btn btn-sm'
                    html += `<button class="${className}" ${disabled} onclick="${action.onClick}('${row.id}')">${action.text}</button>`
                })
                html += '</div></td>'
            }
            html += '</tr>'
        })
    }
    html += '</tbody></table>'
    
    return html
}

// 创建分页组件
function createPagination(pagination, onPageChange) {
    if (!pagination || pagination.total === 0) {
        return ''
    }

    const { total, page, per_page, pages, has_prev, has_next } = pagination
    const start = (page - 1) * per_page + 1
    const end = Math.min(page * per_page, total)

    let html = '<div class="pagination">'
    
    // 信息显示
    html += `<div class="pagination-info">显示 ${start}-${end} 条，共 ${total} 条</div>`
    
    // 上一页按钮
    html += `<button class="pagination-btn" ${!has_prev ? 'disabled' : ''} onclick="${onPageChange}(${page - 1})">
        <i class="fas fa-chevron-left"></i>
    </button>`
    
    // 页码按钮
    const startPage = Math.max(1, page - 2)
    const endPage = Math.min(pages, page + 2)
    
    if (startPage > 1) {
        html += `<button class="pagination-btn" onclick="${onPageChange}(1)">1</button>`
        if (startPage > 2) {
            html += '<span>...</span>'
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === page ? 'active' : ''
        html += `<button class="pagination-btn ${activeClass}" onclick="${onPageChange}(${i})">${i}</button>`
    }
    
    if (endPage < pages) {
        if (endPage < pages - 1) {
            html += '<span>...</span>'
        }
        html += `<button class="pagination-btn" onclick="${onPageChange}(${pages})">${pages}</button>`
    }
    
    // 下一页按钮
    html += `<button class="pagination-btn" ${!has_next ? 'disabled' : ''} onclick="${onPageChange}(${page + 1})">
        <i class="fas fa-chevron-right"></i>
    </button>`
    
    html += '</div>'
    return html
}

// 创建统计卡片
function createStatCard(title, value, icon, iconClass = 'primary') {
    return `
        <div class="stat-card">
            <div class="stat-icon ${iconClass}">
                <i class="${icon}"></i>
            </div>
            <div class="stat-content">
                <h3>${value}</h3>
                <p>${title}</p>
            </div>
        </div>
    `
}

// 创建表单模态框
function createFormModal(title, fields, onSubmit, initialData = {}) {
    let formHTML = `
        <div class="modal-overlay">
            <div class="modal" style="width: 600px;">
                <div class="modal-header">
                    <h3 class="modal-title">${title}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="modal-form">
    `
    
    fields.forEach(field => {
        const value = initialData[field.name] || field.defaultValue || ''
        const required = field.required ? 'required' : ''
        
        formHTML += `<div class="form-group">`
        formHTML += `<label>${field.label}${field.required ? ' *' : ''}</label>`
        
        if (field.type === 'textarea') {
            formHTML += `<textarea name="${field.name}" class="form-control" ${required} placeholder="${field.placeholder || ''}">${value}</textarea>`
        } else if (field.type === 'select') {
            formHTML += `<select name="${field.name}" class="form-control" ${required}>`
            if (field.placeholder) {
                formHTML += `<option value="">${field.placeholder}</option>`
            }
            field.options.forEach(option => {
                const selected = option.value === value ? 'selected' : ''
                formHTML += `<option value="${option.value}" ${selected}>${option.label}</option>`
            })
            formHTML += '</select>'
        } else {
            formHTML += `<input type="${field.type || 'text'}" name="${field.name}" class="form-control" ${required} placeholder="${field.placeholder || ''}" value="${value}">`
        }
        
        formHTML += '</div>'
    })
    
    formHTML += `
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="submitModalForm('${onSubmit}')">确定</button>
                </div>
            </div>
        </div>
    `
    
    document.getElementById('modal-container').innerHTML = formHTML
}

// 提交模态框表单
async function submitModalForm(onSubmitFunction) {
    const form = document.getElementById('modal-form')
    const formData = new FormData(form)
    const data = {}
    
    for (const [key, value] of formData.entries()) {
        data[key] = value
    }
    
    try {
        showLoading()
        await window[onSubmitFunction](data)
        document.querySelector('.modal-overlay').remove()
    } catch (error) {
        showMessage(error.message || '操作失败', 'error')
    } finally {
        hideLoading()
    }
}

// 创建搜索框
function createSearchBox(placeholder = '搜索...', onSearch) {
    return `
        <div class="search-box">
            <input type="text" class="form-control" placeholder="${placeholder}" id="search-input">
            <i class="fas fa-search search-icon"></i>
        </div>
    `
}

// 绑定搜索事件
function bindSearchEvents(onSearch) {
    const searchInput = document.getElementById('search-input')
    if (searchInput) {
        const debouncedSearch = debounce((value) => {
            onSearch(value)
        }, 500)
        
        searchInput.addEventListener('input', (e) => {
            debouncedSearch(e.target.value)
        })
    }
}

// 创建文件上传组件
function createFileUpload(accept = '*', multiple = false, onFileSelect) {
    const uploadId = 'upload-' + Date.now()
    
    const html = `
        <div class="upload-area" onclick="document.getElementById('${uploadId}').click()">
            <input type="file" id="${uploadId}" style="display: none;" accept="${accept}" ${multiple ? 'multiple' : ''}>
            <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="upload-text">点击或拖拽文件到此处上传</div>
            <div class="upload-hint">支持的文件格式: ${accept}</div>
        </div>
    `
    
    // 绑定事件需要在插入DOM后进行
    setTimeout(() => {
        const fileInput = document.getElementById(uploadId)
        const uploadArea = fileInput.parentElement
        
        fileInput.addEventListener('change', (e) => {
            if (onFileSelect) {
                onFileSelect(e.target.files)
            }
        })
        
        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault()
            uploadArea.classList.add('dragover')
        })
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover')
        })
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault()
            uploadArea.classList.remove('dragover')
            if (onFileSelect) {
                onFileSelect(e.dataTransfer.files)
            }
        })
    }, 0)
    
    return html
}
