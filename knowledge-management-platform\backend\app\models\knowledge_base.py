from app import db
from .base import BaseModel

class KnowledgeBase(BaseModel):
    """知识库模型"""
    __tablename__ = 'knowledge_bases'
    
    name = db.Column(db.String(255), nullable=False, comment='知识库名称')
    description = db.Column(db.Text, comment='知识库描述')
    ragflow_kb_id = db.Column(db.String(255), unique=True, comment='RAGFlow知识库ID')
    
    # 配置信息
    chunk_method = db.Column(db.String(50), default='naive', comment='分块方法')
    parser_config = db.Column(db.JSON, comment='解析配置')
    
    # 统计信息
    document_count = db.Column(db.Integer, default=0, comment='文档数量')
    chunk_count = db.Column(db.Integer, default=0, comment='分块数量')
    
    # 状态
    status = db.Column(db.String(20), default='active', comment='状态: active, inactive')
    
    # 关联关系
    documents = db.relationship('Document', backref='knowledge_base', lazy='dynamic')
    
    def __repr__(self):
        return f'<KnowledgeBase {self.name}>'
    
    def to_dict(self):
        """转换为字典，包含统计信息"""
        data = super().to_dict()
        data['document_count'] = self.documents.filter_by(is_deleted=False).count()
        return data
