// 仪表盘页面

class DashboardPage extends BasePage {
    constructor() {
        super()
        this.stats = null
    }

    async render() {
        this.showLoading()
        
        try {
            // 获取统计数据
            await this.loadStatistics()
            
            const html = `
                <div class="page-header">
                    <h1 class="page-title">仪表盘</h1>
                    <p class="page-description">系统概览和统计信息</p>
                </div>
                
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    ${this.renderStatCards()}
                </div>
                
                <!-- 快速操作 -->
                <div class="card" style="margin-bottom: 24px;">
                    <div class="card-header">
                        <h3 class="card-title">快速操作</h3>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                            <button class="btn btn-primary" onclick="app.loadPage('knowledge-base')" style="height: 60px;">
                                <i class="fas fa-database" style="font-size: 20px; margin-bottom: 8px;"></i>
                                <br>管理知识库
                            </button>
                            <button class="btn btn-success" onclick="app.loadPage('chat-assistant')" style="height: 60px;">
                                <i class="fas fa-robot" style="font-size: 20px; margin-bottom: 8px;"></i>
                                <br>创建助手
                            </button>
                            <button class="btn btn-warning" onclick="app.loadPage('sensitive-word')" style="height: 60px;">
                                <i class="fas fa-shield-alt" style="font-size: 20px; margin-bottom: 8px;"></i>
                                <br>敏感词管理
                            </button>
                            ${authManager.isAdmin() ? `
                            <button class="btn btn-danger" onclick="app.loadPage('user-management')" style="height: 60px;">
                                <i class="fas fa-users" style="font-size: 20px; margin-bottom: 8px;"></i>
                                <br>用户管理
                            </button>
                            ` : ''}
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">系统信息</h3>
                        <button class="btn btn-sm" onclick="refreshStats()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        ${this.renderSystemInfo()}
                    </div>
                </div>
            `
            
            this.renderContent(html)
        } catch (error) {
            this.showError(error.message || '加载失败')
        }
    }

    async loadStatistics() {
        try {
            // 并行获取各模块统计数据
            const [kbStats, assistantStats, wordStats, userStats] = await Promise.all([
                this.getKnowledgeBaseStats(),
                this.getChatAssistantStats(),
                this.getSensitiveWordStats(),
                this.getUserStats()
            ])

            this.stats = {
                knowledgeBase: kbStats,
                chatAssistant: assistantStats,
                sensitiveWord: wordStats,
                user: userStats
            }
        } catch (error) {
            console.error('Failed to load statistics:', error)
            this.stats = {
                knowledgeBase: { total: 0, active: 0 },
                chatAssistant: { total: 0, active: 0 },
                sensitiveWord: { total: 0, active: 0 },
                user: { total: 0, active: 0 }
            }
        }
    }

    async getKnowledgeBaseStats() {
        try {
            const data = await knowledgeBaseAPI.list({ per_page: 1 })
            return {
                total: data.pagination.total,
                active: data.pagination.total // 简化统计
            }
        } catch (error) {
            return { total: 0, active: 0 }
        }
    }

    async getChatAssistantStats() {
        try {
            const data = await chatAssistantAPI.list({ per_page: 1 })
            return {
                total: data.pagination.total,
                active: data.pagination.total // 简化统计
            }
        } catch (error) {
            return { total: 0, active: 0 }
        }
    }

    async getSensitiveWordStats() {
        try {
            const data = await sensitiveWordAPI.getStatistics()
            return {
                total: data.total_words || 0,
                active: data.active_words || 0
            }
        } catch (error) {
            return { total: 0, active: 0 }
        }
    }

    async getUserStats() {
        try {
            if (authManager.isAdmin()) {
                const data = await userAPI.getStatistics()
                return {
                    total: data.total_users || 0,
                    active: data.active_users || 0
                }
            }
            return { total: 0, active: 0 }
        } catch (error) {
            return { total: 0, active: 0 }
        }
    }

    renderStatCards() {
        if (!this.stats) return ''

        const cards = [
            {
                title: '知识库总数',
                value: this.stats.knowledgeBase.total,
                icon: 'fas fa-database',
                iconClass: 'primary'
            },
            {
                title: '聊天助手',
                value: this.stats.chatAssistant.total,
                icon: 'fas fa-robot',
                iconClass: 'success'
            },
            {
                title: '敏感词库',
                value: this.stats.sensitiveWord.total,
                icon: 'fas fa-shield-alt',
                iconClass: 'warning'
            }
        ]

        // 如果是管理员，显示用户统计
        if (authManager.isAdmin()) {
            cards.push({
                title: '用户总数',
                value: this.stats.user.total,
                icon: 'fas fa-users',
                iconClass: 'danger'
            })
        }

        return cards.map(card => createStatCard(card.title, card.value, card.icon, card.iconClass)).join('')
    }

    renderSystemInfo() {
        const user = authManager.getCurrentUser()

        return `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="margin-bottom: 12px; color: #333;">当前用户</h4>
                    <p><strong>用户名:</strong> ${user.username}</p>
                    <p><strong>昵称:</strong> ${user.nickname || '-'}</p>
                    <p><strong>邮箱:</strong> ${user.email}</p>
                    <p><strong>角色:</strong> ${user.role === 'admin' ? '管理员' : '普通用户'}</p>
                </div>
                <div>
                    <h4 style="margin-bottom: 12px; color: #333;">RAGFlow连接</h4>
                    <p><strong>服务器:</strong> http://117.72.181.138:180</p>
                    <p><strong>状态:</strong> <span class="status-tag active">已连接</span></p>
                    <p><strong>API版本:</strong> v1</p>
                </div>
                <div>
                    <h4 style="margin-bottom: 12px; color: #333;">系统版本</h4>
                    <p><strong>平台版本:</strong> v1.0.0</p>
                    <p><strong>构建时间:</strong> ${new Date().toLocaleDateString()}</p>
                    <p><strong>运行环境:</strong> 生产环境</p>
                </div>
            </div>
        `
    }

    bindEvents() {
        // 绑定刷新按钮事件
        window.refreshStats = async () => {
            await this.render()
            showMessage('统计数据已刷新', 'success')
        }
    }
}
