from app import db
from .base import BaseModel

class Document(BaseModel):
    """文档模型"""
    __tablename__ = 'documents'
    
    name = db.Column(db.String(255), nullable=False, comment='文档名称')
    original_filename = db.Column(db.String(255), comment='原始文件名')
    file_type = db.Column(db.String(50), comment='文件类型')
    file_size = db.Column(db.BigInteger, comment='文件大小(字节)')
    file_path = db.Column(db.String(500), comment='文件路径')
    
    # RAGFlow相关
    ragflow_doc_id = db.Column(db.String(255), unique=True, comment='RAGFlow文档ID')
    knowledge_base_id = db.Column(db.String(36), db.<PERSON>ey('knowledge_bases.id'), nullable=False)
    
    # 解析状态
    parse_status = db.Column(db.String(20), default='pending', comment='解析状态: pending, processing, completed, failed')
    parse_progress = db.Column(db.Float, default=0.0, comment='解析进度')
    parse_error = db.Column(db.Text, comment='解析错误信息')
    
    # 统计信息
    chunk_count = db.Column(db.Integer, default=0, comment='分块数量')
    token_count = db.Column(db.Integer, default=0, comment='Token数量')
    
    def __repr__(self):
        return f'<Document {self.name}>'
    
    @property
    def file_size_mb(self):
        """文件大小(MB)"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0
