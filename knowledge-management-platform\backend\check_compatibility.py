#!/usr/bin/env python3
"""
Python版本兼容性检查脚本
检查当前Python版本并提供兼容性建议
"""

import sys
import subprocess
import pkg_resources
from packaging import version

def check_python_version():
    """检查Python版本"""
    current_version = sys.version_info
    print(f"当前Python版本: {current_version.major}.{current_version.minor}.{current_version.micro}")
    
    if current_version >= (3, 13):
        print("⚠️  警告: Python 3.13可能与某些依赖包不兼容")
        print("建议使用Python 3.8-3.12版本")
        return False
    elif current_version < (3, 8):
        print("❌ 错误: Python版本过低，需要Python 3.8+")
        return False
    else:
        print("✅ Python版本兼容")
        return True

def check_installed_packages():
    """检查已安装的包"""
    try:
        import flask
        print(f"Flask版本: {flask.__version__}")
    except ImportError:
        print("Flask未安装")
        return False
    
    try:
        import sqlalchemy
        print(f"SQLAlchemy版本: {sqlalchemy.__version__}")
        
        # 检查SQLAlchemy版本兼容性
        if version.parse(sqlalchemy.__version__) >= version.parse("2.0.0"):
            if sys.version_info >= (3, 13):
                print("⚠️  警告: SQLAlchemy 2.0+ 与 Python 3.13 可能不兼容")
                return False
    except ImportError:
        print("SQLAlchemy未安装")
        return False
    
    return True

def install_compatible_packages():
    """安装兼容的包版本"""
    print("\n正在安装兼容的包版本...")
    
    # 根据Python版本选择合适的包版本
    if sys.version_info >= (3, 13):
        # Python 3.13兼容版本
        packages = [
            "Flask==2.3.3",
            "Flask-CORS==4.0.0", 
            "Flask-SQLAlchemy==2.5.1",
            "Flask-Migrate==3.1.0",
            "Flask-JWT-Extended==4.5.3",
            "SQLAlchemy==1.4.53",
            "PyMySQL==1.1.0",
            "redis==5.0.1",
            "requests==2.31.0",
            "python-dotenv==1.0.0",
            "gunicorn==21.2.0",
            "Werkzeug==2.3.7"
        ]
    else:
        # 其他Python版本
        packages = [
            "Flask==2.3.3",
            "Flask-CORS==4.0.0",
            "Flask-SQLAlchemy==3.0.5", 
            "Flask-Migrate==4.0.5",
            "Flask-JWT-Extended==4.6.0",
            "SQLAlchemy==2.0.23",
            "PyMySQL==1.1.0",
            "redis==5.0.1",
            "requests==2.31.0",
            "python-dotenv==1.0.0",
            "gunicorn==21.2.0",
            "Werkzeug==2.3.7"
        ]
    
    try:
        for package in packages:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        
        print("✅ 所有包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False

def main():
    print("=" * 50)
    print("知识库管理平台 - 兼容性检查")
    print("=" * 50)
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查已安装的包
    packages_ok = check_installed_packages()
    
    if not python_ok:
        print("\n建议:")
        print("1. 使用pyenv或conda安装Python 3.8-3.12")
        print("2. 创建虚拟环境: python -m venv venv")
        print("3. 激活虚拟环境并重新安装依赖")
        return
    
    if not packages_ok:
        print("\n是否要自动安装兼容的包版本? (y/n): ", end="")
        choice = input().lower().strip()
        
        if choice in ['y', 'yes']:
            if install_compatible_packages():
                print("\n✅ 兼容性问题已修复，可以运行应用了")
            else:
                print("\n❌ 自动修复失败，请手动安装依赖")
        else:
            print("\n请手动安装requirements.txt中的依赖:")
            print("pip install -r requirements.txt")
    else:
        print("\n✅ 所有检查通过，可以运行应用")

if __name__ == "__main__":
    main()
