from flask import request
from app.api import api_bp
from app.models.user import User
from app.utils.response import success_response, error_response, paginated_response
from app.utils.decorators import login_required, admin_required, validate_json
from app.utils.validators import validate_email, validate_password
import logging

logger = logging.getLogger(__name__)

@api_bp.route('/users', methods=['GET'])
@admin_required
def list_users():
    """获取用户列表（管理员）"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '').strip()
    role = request.args.get('role', '').strip()
    status = request.args.get('status', '').strip()
    
    # 构建查询
    query = User.query.filter_by(is_deleted=False)
    
    if search:
        query = query.filter(
            (User.username.contains(search)) |
            (User.email.contains(search)) |
            (User.nickname.contains(search))
        )
    
    if role:
        query = query.filter_by(role=role)
    
    if status:
        query = query.filter_by(status=status)
    
    # 按创建时间倒序排列
    query = query.order_by(User.created_at.desc())
    
    # 分页查询
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 转换为字典
    items = [user.to_dict() for user in pagination.items]
    
    return paginated_response(
        items=items,
        total=pagination.total,
        page=page,
        per_page=per_page
    )

@api_bp.route('/users', methods=['POST'])
@admin_required
@validate_json('username', 'email', 'password')
def create_user():
    """创建用户（管理员）"""
    data = request.get_json()
    username = data['username']
    email = data['email']
    password = data['password']
    
    # 验证邮箱格式
    if not validate_email(email):
        return error_response("邮箱格式不正确")
    
    # 验证密码强度
    password_errors = validate_password(password)
    if password_errors:
        return error_response("密码不符合要求: " + "; ".join(password_errors))
    
    # 检查用户名是否已存在
    if User.query.filter_by(username=username, is_deleted=False).first():
        return error_response("用户名已存在")
    
    # 检查邮箱是否已存在
    if User.query.filter_by(email=email, is_deleted=False).first():
        return error_response("邮箱已被注册")
    
    try:
        user = User(
            username=username,
            email=email,
            nickname=data.get('nickname', username),
            phone=data.get('phone', ''),
            role=data.get('role', 'user'),
            status=data.get('status', 'active')
        )
        user.set_password(password)
        user.save()
        
        logger.info(f"Admin created user: {username}")
        return success_response(user.to_dict(), "用户创建成功")
        
    except Exception as e:
        logger.error(f"Failed to create user: {str(e)}")
        return error_response(f"创建用户失败: {str(e)}")

@api_bp.route('/users/<user_id>', methods=['GET'])
@admin_required
def get_user(user_id):
    """获取用户详情（管理员）"""
    user = User.get_by_id(user_id)
    if not user:
        return error_response("用户不存在", 404)
    
    return success_response(user.to_dict())

@api_bp.route('/users/<user_id>', methods=['PUT'])
@admin_required
def update_user(user_id):
    """更新用户（管理员）"""
    user = User.get_by_id(user_id)
    if not user:
        return error_response("用户不存在", 404)
    
    data = request.get_json() or {}
    
    # 检查用户名是否重复
    if 'username' in data and data['username'] != user.username:
        if User.query.filter_by(username=data['username'], is_deleted=False).first():
            return error_response("用户名已存在")
    
    # 检查邮箱是否重复
    if 'email' in data and data['email'] != user.email:
        if not validate_email(data['email']):
            return error_response("邮箱格式不正确")
        if User.query.filter_by(email=data['email'], is_deleted=False).first():
            return error_response("邮箱已被注册")
    
    try:
        # 更新字段
        updatable_fields = ['username', 'email', 'nickname', 'phone', 'role', 'status']
        for field in updatable_fields:
            if field in data:
                setattr(user, field, data[field])
        
        # 更新密码
        if 'password' in data and data['password']:
            password_errors = validate_password(data['password'])
            if password_errors:
                return error_response("密码不符合要求: " + "; ".join(password_errors))
            user.set_password(data['password'])
        
        user.save()
        
        logger.info(f"Admin updated user: {user.username} (ID: {user.id})")
        return success_response(user.to_dict(), "用户更新成功")
        
    except Exception as e:
        logger.error(f"Failed to update user: {str(e)}")
        return error_response(f"更新用户失败: {str(e)}")

@api_bp.route('/users/<user_id>', methods=['DELETE'])
@admin_required
def delete_user(user_id):
    """删除用户（管理员）"""
    user = User.get_by_id(user_id)
    if not user:
        return error_response("用户不存在", 404)
    
    # 不能删除管理员账户
    if user.is_admin:
        return error_response("不能删除管理员账户")
    
    try:
        user.delete()
        
        logger.info(f"Admin deleted user: {user.username} (ID: {user.id})")
        return success_response(message="用户删除成功")
        
    except Exception as e:
        logger.error(f"Failed to delete user: {str(e)}")
        return error_response(f"删除用户失败: {str(e)}")

@api_bp.route('/users/batch-delete', methods=['POST'])
@admin_required
@validate_json('ids')
def batch_delete_users():
    """批量删除用户（管理员）"""
    data = request.get_json()
    ids = data['ids']
    
    if not isinstance(ids, list) or not ids:
        return error_response("ID列表不能为空")
    
    try:
        deleted_count = 0
        skipped_count = 0
        
        for user_id in ids:
            user = User.get_by_id(user_id)
            if user:
                # 跳过管理员账户
                if user.is_admin:
                    skipped_count += 1
                    continue
                
                user.delete()
                deleted_count += 1
        
        logger.info(f"Admin batch deleted {deleted_count} users, skipped {skipped_count}")
        return success_response({
            'deleted_count': deleted_count,
            'skipped_count': skipped_count
        }, f"批量删除完成，成功删除{deleted_count}个用户，跳过{skipped_count}个管理员")
        
    except Exception as e:
        logger.error(f"Failed to batch delete users: {str(e)}")
        return error_response(f"批量删除用户失败: {str(e)}")

@api_bp.route('/users/<user_id>/reset-password', methods=['POST'])
@admin_required
@validate_json('new_password')
def reset_user_password(user_id):
    """重置用户密码（管理员）"""
    user = User.get_by_id(user_id)
    if not user:
        return error_response("用户不存在", 404)
    
    data = request.get_json()
    new_password = data['new_password']
    
    # 验证密码强度
    password_errors = validate_password(new_password)
    if password_errors:
        return error_response("密码不符合要求: " + "; ".join(password_errors))
    
    try:
        user.set_password(new_password)
        user.save()
        
        logger.info(f"Admin reset password for user: {user.username}")
        return success_response(message="密码重置成功")
        
    except Exception as e:
        logger.error(f"Failed to reset user password: {str(e)}")
        return error_response(f"重置密码失败: {str(e)}")

@api_bp.route('/users/statistics', methods=['GET'])
@admin_required
def get_user_statistics():
    """获取用户统计（管理员）"""
    try:
        total_users = User.query.filter_by(is_deleted=False).count()
        active_users = User.query.filter_by(status='active', is_deleted=False).count()
        admin_users = User.query.filter_by(role='admin', is_deleted=False).count()
        
        # 按状态统计
        status_stats = {}
        for status in ['active', 'inactive', 'banned']:
            count = User.query.filter_by(status=status, is_deleted=False).count()
            status_stats[status] = count
        
        # 最近注册的用户
        recent_users = User.query.filter_by(is_deleted=False).order_by(
            User.created_at.desc()
        ).limit(10).all()
        
        # 最近登录的用户
        recent_logins = User.query.filter(
            User.last_login_at.isnot(None),
            User.is_deleted == False
        ).order_by(User.last_login_at.desc()).limit(10).all()
        
        stats = {
            'total_users': total_users,
            'active_users': active_users,
            'admin_users': admin_users,
            'regular_users': total_users - admin_users,
            'status_stats': status_stats,
            'recent_users': [user.to_dict() for user in recent_users],
            'recent_logins': [user.to_dict() for user in recent_logins]
        }
        
        return success_response(stats)
        
    except Exception as e:
        logger.error(f"Failed to get user statistics: {str(e)}")
        return error_response(f"获取用户统计失败: {str(e)}")
