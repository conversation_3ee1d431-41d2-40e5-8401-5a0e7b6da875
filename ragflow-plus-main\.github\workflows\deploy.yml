name: Deploy Docs

on:
  push:
    branches:
      - main
    paths:
      - 'docs/**'
  pull_request:
    branches:
      - main
    paths:
      - 'docs/**'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Debug modified files (for troubleshooting)
        run: |
          echo "Modified files in push/PR:"
          if [[ $GITHUB_EVENT_NAME == 'push' ]]; then
            echo "${{ toJSON(github.event.head_commit.modified_files) }}"
          else
            echo "${{ toJSON(github.event.pull_request.changed_files) }}"
          fi
