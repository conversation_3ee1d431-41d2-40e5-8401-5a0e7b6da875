from flask import request
from app.api import api_bp
from app.models.conversation import Conversation, Message
from app.models.chat_assistant import ChatAssistant
from app.utils.response import success_response, error_response, paginated_response
from app.utils.decorators import login_required, validate_json
from app.utils.ragflow_client import ragflow_client
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

@api_bp.route('/chat-assistants/<assistant_id>/conversations', methods=['GET'])
@login_required
def list_conversations(assistant_id):
    """获取对话列表"""
    # 验证助手是否存在
    assistant = ChatAssistant.get_by_id(assistant_id)
    if not assistant:
        return error_response("聊天助手不存在", 404)
    
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '').strip()
    status = request.args.get('status', '').strip()
    
    # 构建查询
    query = Conversation.query.filter_by(assistant_id=assistant_id, is_deleted=False)
    
    if search:
        query = query.filter(Conversation.title.contains(search))
    
    if status:
        query = query.filter_by(status=status)
    
    # 按最后消息时间倒序排列
    query = query.order_by(Conversation.last_message_at.desc().nullslast(), 
                          Conversation.created_at.desc())
    
    # 分页查询
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 转换为字典
    items = [conv.to_dict() for conv in pagination.items]
    
    return paginated_response(
        items=items,
        total=pagination.total,
        page=page,
        per_page=per_page
    )

@api_bp.route('/chat-assistants/<assistant_id>/conversations', methods=['POST'])
@login_required
def create_conversation(assistant_id):
    """创建对话"""
    # 验证助手是否存在
    assistant = ChatAssistant.get_by_id(assistant_id)
    if not assistant:
        return error_response("聊天助手不存在", 404)
    
    data = request.get_json() or {}
    title = data.get('title', f"对话 - {datetime.now().strftime('%Y-%m-%d %H:%M')}")
    
    try:
        # 在RAGFlow中创建对话
        ragflow_data = None
        if assistant.ragflow_assistant_id:
            ragflow_data = ragflow_client.create_conversation(
                chat_id=assistant.ragflow_assistant_id,
                name=title
            )
        
        # 在本地数据库中创建记录
        conversation = Conversation(
            title=title,
            assistant_id=assistant_id,
            ragflow_conversation_id=ragflow_data.get('id') if ragflow_data else None,
            status='active'
        )
        conversation.save()
        
        logger.info(f"Created conversation: {title} for assistant {assistant.name}")
        return success_response(conversation.to_dict(), "对话创建成功")
        
    except Exception as e:
        logger.error(f"Failed to create conversation: {str(e)}")
        return error_response(f"创建对话失败: {str(e)}")

@api_bp.route('/conversations/<conversation_id>', methods=['GET'])
@login_required
def get_conversation(conversation_id):
    """获取对话详情"""
    conversation = Conversation.get_by_id(conversation_id)
    if not conversation:
        return error_response("对话不存在", 404)
    
    return success_response(conversation.to_dict())

@api_bp.route('/conversations/<conversation_id>', methods=['PUT'])
@login_required
def update_conversation(conversation_id):
    """更新对话"""
    conversation = Conversation.get_by_id(conversation_id)
    if not conversation:
        return error_response("对话不存在", 404)
    
    data = request.get_json() or {}
    
    # 更新本地数据库
    updatable_fields = ['title', 'status']
    for field in updatable_fields:
        if field in data:
            setattr(conversation, field, data[field])
    
    conversation.save()
    
    logger.info(f"Updated conversation: {conversation.title} (ID: {conversation.id})")
    return success_response(conversation.to_dict(), "对话更新成功")

@api_bp.route('/conversations/<conversation_id>', methods=['DELETE'])
@login_required
def delete_conversation(conversation_id):
    """删除对话"""
    conversation = Conversation.get_by_id(conversation_id)
    if not conversation:
        return error_response("对话不存在", 404)
    
    try:
        # 软删除本地记录
        conversation.delete()
        
        # 同时删除相关消息
        Message.query.filter_by(conversation_id=conversation_id).update({'is_deleted': True})
        
        logger.info(f"Deleted conversation: {conversation.title} (ID: {conversation.id})")
        return success_response(message="对话删除成功")
        
    except Exception as e:
        logger.error(f"Failed to delete conversation: {str(e)}")
        return error_response(f"删除对话失败: {str(e)}")

@api_bp.route('/conversations/<conversation_id>/messages', methods=['GET'])
@login_required
def get_conversation_messages(conversation_id):
    """获取对话消息"""
    conversation = Conversation.get_by_id(conversation_id)
    if not conversation:
        return error_response("对话不存在", 404)
    
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 50, type=int), 100)
    
    # 构建查询
    query = Message.query.filter_by(conversation_id=conversation_id, is_deleted=False)
    query = query.order_by(Message.created_at.asc())
    
    # 分页查询
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 转换为字典
    items = [msg.to_dict() for msg in pagination.items]
    
    return paginated_response(
        items=items,
        total=pagination.total,
        page=page,
        per_page=per_page
    )

@api_bp.route('/conversations/<conversation_id>/messages', methods=['POST'])
@login_required
@validate_json('content')
def send_message(conversation_id):
    """发送消息"""
    conversation = Conversation.get_by_id(conversation_id)
    if not conversation:
        return error_response("对话不存在", 404)
    
    data = request.get_json()
    content = data['content']
    stream = data.get('stream', False)
    
    try:
        # 保存用户消息
        user_message = Message(
            conversation_id=conversation_id,
            content=content,
            role='user'
        )
        user_message.save()
        
        # 发送到RAGFlow获取回复
        response_data = {}
        if conversation.ragflow_conversation_id:
            response_data = ragflow_client.send_message(
                conversation_id=conversation.ragflow_conversation_id,
                message=content,
                stream=stream
            )
        
        # 保存助手回复
        assistant_content = response_data.get('answer', '抱歉，我暂时无法回答这个问题。')
        assistant_message = Message(
            conversation_id=conversation_id,
            content=assistant_content,
            role='assistant',
            references=response_data.get('reference', []),
            response_time=response_data.get('response_time', 0)
        )
        assistant_message.save()
        
        # 更新对话信息
        conversation.message_count += 2
        conversation.last_message_at = datetime.utcnow()
        conversation.save()
        
        # 更新助手统计
        assistant = conversation.chat_assistant
        assistant.message_count += 2
        assistant.save()
        
        result = {
            'user_message': user_message.to_dict(),
            'assistant_message': assistant_message.to_dict()
        }
        
        logger.info(f"Sent message in conversation {conversation.title}")
        return success_response(result, "消息发送成功")
        
    except Exception as e:
        logger.error(f"Failed to send message: {str(e)}")
        return error_response(f"发送消息失败: {str(e)}")

@api_bp.route('/conversations/<conversation_id>/clear', methods=['POST'])
@login_required
def clear_conversation(conversation_id):
    """清空对话"""
    conversation = Conversation.get_by_id(conversation_id)
    if not conversation:
        return error_response("对话不存在", 404)
    
    try:
        # 软删除所有消息
        Message.query.filter_by(conversation_id=conversation_id).update({'is_deleted': True})
        
        # 重置对话统计
        conversation.message_count = 0
        conversation.last_message_at = None
        conversation.save()
        
        logger.info(f"Cleared conversation: {conversation.title} (ID: {conversation.id})")
        return success_response(message="对话已清空")
        
    except Exception as e:
        logger.error(f"Failed to clear conversation: {str(e)}")
        return error_response(f"清空对话失败: {str(e)}")

@api_bp.route('/conversations/<conversation_id>/export', methods=['GET'])
@login_required
def export_conversation(conversation_id):
    """导出对话"""
    conversation = Conversation.get_by_id(conversation_id)
    if not conversation:
        return error_response("对话不存在", 404)
    
    try:
        # 获取所有消息
        messages = Message.query.filter_by(
            conversation_id=conversation_id, 
            is_deleted=False
        ).order_by(Message.created_at.asc()).all()
        
        export_data = {
            'conversation': conversation.to_dict(),
            'messages': [msg.to_dict() for msg in messages],
            'export_time': datetime.utcnow().isoformat()
        }
        
        return success_response(export_data, "导出成功")
        
    except Exception as e:
        logger.error(f"Failed to export conversation: {str(e)}")
        return error_response(f"导出对话失败: {str(e)}")
