// 初始化脚本 - 确保所有依赖正确加载

// 检查必要的全局对象是否存在
function checkDependencies() {
    const dependencies = [
        { name: 'formatDate', type: 'function', source: 'utils.js' },
        { name: 'api', type: 'object', source: 'api.js' },
        { name: 'authManager', type: 'object', source: 'auth.js' },
        { name: 'createDataTable', type: 'function', source: 'components.js' },
        { name: 'App', type: 'function', source: 'app.js' },
        { name: 'BasePage', type: 'function', source: 'app.js' },
        { name: 'DashboardPage', type: 'function', source: 'pages/dashboard.js' },
        { name: 'KnowledgeBasePage', type: 'function', source: 'pages/knowledge-base.js' },
        { name: 'ChatAssistantPage', type: 'function', source: 'pages/chat-assistant.js' },
        { name: 'SensitiveWordPage', type: 'function', source: 'pages/sensitive-word.js' },
        { name: 'UserManagementPage', type: 'function', source: 'pages/user-management.js' }
    ]

    const missing = []
    const errors = []

    dependencies.forEach(dep => {
        try {
            if (typeof window[dep.name] === 'undefined') {
                missing.push(`${dep.name} (${dep.source})`)
            } else if (typeof window[dep.name] !== dep.type) {
                errors.push(`${dep.name} should be ${dep.type} but is ${typeof window[dep.name]} (${dep.source})`)
            }
        } catch (error) {
            errors.push(`Error checking ${dep.name}: ${error.message}`)
        }
    })

    if (missing.length > 0) {
        console.error('Missing dependencies:', missing)
    }

    if (errors.length > 0) {
        console.error('Dependency errors:', errors)
    }

    return missing.length === 0 && errors.length === 0
}

// 初始化应用
function initializeApp() {
    try {
        // 检查依赖
        if (!checkDependencies()) {
            console.error('Dependencies check failed')
            return false
        }

        // 初始化认证管理器
        if (typeof authManager !== 'undefined') {
            authManager.initPageDisplay()
        }

        // 初始化应用
        if (typeof App !== 'undefined') {
            window.app = new App()
        }

        console.log('Application initialized successfully')
        return true
    } catch (error) {
        console.error('Failed to initialize application:', error)
        return false
    }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp)
} else {
    initializeApp()
}
