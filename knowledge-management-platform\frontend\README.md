# 前端开发指南

## 项目结构

```
frontend/
├── css/                    # 样式文件
│   ├── style.css          # 主样式
│   └── components.css     # 组件样式
├── js/                    # JavaScript文件
│   ├── error-handler.js   # 错误处理
│   ├── utils.js           # 工具函数
│   ├── api.js             # API服务
│   ├── auth.js            # 认证管理
│   ├── components.js      # 通用组件
│   ├── app.js             # 主应用
│   ├── init.js            # 初始化脚本
│   └── pages/             # 页面逻辑
│       ├── dashboard.js
│       ├── knowledge-base.js
│       ├── chat-assistant.js
│       ├── sensitive-word.js
│       └── user-management.js
├── index.html             # 主页面
├── test.html              # 测试页面
├── start-dev.py           # 开发服务器
└── README.md              # 说明文档
```

## 快速启动

### 方式一：使用Python开发服务器（推荐）

```bash
cd frontend
python start-dev.py
```

服务器将在 http://localhost:3000 启动，并自动打开浏览器。

### 方式二：使用Python内置服务器

```bash
cd frontend
python -m http.server 3000
```

### 方式三：使用Node.js工具

```bash
cd frontend
# 安装live-server
npm install -g live-server

# 启动服务器
live-server --port=3000
```

### 方式四：使用其他HTTP服务器

任何能够提供静态文件服务的HTTP服务器都可以使用。

## 功能测试

访问 http://localhost:3000/test.html 可以测试所有JavaScript文件是否正确加载。

## 开发说明

### 技术栈
- **HTML5** - 页面结构
- **CSS3** - 样式和布局
- **JavaScript ES6+** - 交互逻辑
- **Font Awesome** - 图标库

### 架构设计
- **单页应用** - 所有功能在一个页面中切换
- **模块化设计** - 每个功能模块独立的JavaScript文件
- **组件化UI** - 可复用的UI组件
- **响应式布局** - 支持多种设备尺寸

### 核心模块

#### 1. 错误处理 (error-handler.js)
- 全局错误捕获
- 网络错误处理
- API错误处理
- 用户友好的错误提示

#### 2. 工具函数 (utils.js)
- 日期格式化
- 文件大小格式化
- 消息提示
- 确认对话框
- 表单验证

#### 3. API服务 (api.js)
- 统一的HTTP请求封装
- 自动认证头处理
- 错误响应处理
- 各模块API接口

#### 4. 认证管理 (auth.js)
- 用户登录/登出
- 认证状态管理
- 权限检查
- 用户信息存储

#### 5. 通用组件 (components.js)
- 数据表格组件
- 分页组件
- 模态框组件
- 表单组件
- 统计卡片组件

#### 6. 主应用 (app.js)
- 应用初始化
- 页面路由管理
- 导航控制
- 基础页面类

#### 7. 页面模块 (pages/*.js)
- 各功能模块的页面逻辑
- 数据加载和渲染
- 用户交互处理
- 表单提交处理

### 开发规范

#### JavaScript规范
- 使用ES6+语法
- 采用类和模块化设计
- 统一的命名规范（驼峰命名）
- 详细的注释说明

#### CSS规范
- BEM命名规范
- 响应式设计
- 统一的颜色和字体
- 组件化样式

#### HTML规范
- 语义化标签
- 无障碍访问支持
- SEO友好
- 性能优化

### 调试技巧

#### 1. 浏览器开发者工具
- Console面板查看错误和日志
- Network面板监控API请求
- Elements面板调试样式
- Sources面板调试JavaScript

#### 2. 错误排查
- 检查控制台错误信息
- 验证API请求和响应
- 确认认证状态
- 检查网络连接

#### 3. 性能优化
- 减少HTTP请求
- 压缩静态资源
- 使用缓存策略
- 优化图片资源

### 常见问题

#### 1. 页面空白
- 检查JavaScript错误
- 验证文件路径
- 确认服务器启动

#### 2. API请求失败
- 检查后端服务状态
- 验证API地址配置
- 确认认证信息

#### 3. 样式显示异常
- 检查CSS文件加载
- 验证样式语法
- 确认浏览器兼容性

#### 4. 功能无响应
- 检查事件绑定
- 验证函数定义
- 确认依赖加载

### 扩展开发

#### 添加新页面
1. 在 `js/pages/` 目录创建新的页面文件
2. 继承 `BasePage` 类
3. 实现 `render()` 方法
4. 在 `app.js` 中注册页面
5. 添加导航菜单项

#### 添加新组件
1. 在 `components.js` 中定义组件函数
2. 编写对应的CSS样式
3. 在页面中使用组件
4. 添加必要的事件处理

#### 集成第三方库
1. 通过CDN引入库文件
2. 在HTML中添加script标签
3. 在JavaScript中使用库功能
4. 处理库的依赖关系

### 部署说明

#### 开发环境
- 使用开发服务器
- 启用详细日志
- 保留调试信息

#### 生产环境
- 压缩静态资源
- 启用缓存策略
- 配置HTTPS
- 设置错误监控
