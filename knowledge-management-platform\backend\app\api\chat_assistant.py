from flask import request
from app.api import api_bp
from app.models.chat_assistant import ChatAssistant
from app.models.knowledge_base import KnowledgeBase
from app.utils.response import success_response, error_response, paginated_response
from app.utils.decorators import login_required, validate_json
from app.utils.ragflow_client import ragflow_client
import logging
import json

logger = logging.getLogger(__name__)

@api_bp.route('/chat-assistants', methods=['GET'])
@login_required
def list_chat_assistants():
    """获取聊天助手列表"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '').strip()
    status = request.args.get('status', '').strip()
    
    # 构建查询
    query = ChatAssistant.query.filter_by(is_deleted=False)
    
    if search:
        query = query.filter(ChatAssistant.name.contains(search))
    
    if status:
        query = query.filter_by(status=status)
    
    # 按创建时间倒序排列
    query = query.order_by(ChatAssistant.created_at.desc())
    
    # 分页查询
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 转换为字典
    items = [assistant.to_dict() for assistant in pagination.items]
    
    return paginated_response(
        items=items,
        total=pagination.total,
        page=page,
        per_page=per_page
    )

@api_bp.route('/chat-assistants', methods=['POST'])
@login_required
@validate_json('name')
def create_chat_assistant():
    """创建聊天助手"""
    data = request.get_json()
    name = data['name']
    description = data.get('description', '')
    knowledge_base_ids = data.get('knowledge_base_ids', [])
    
    # 检查名称是否已存在
    if ChatAssistant.query.filter_by(name=name, is_deleted=False).first():
        return error_response("助手名称已存在")
    
    # 验证知识库ID
    if knowledge_base_ids:
        kb_count = KnowledgeBase.query.filter(
            KnowledgeBase.id.in_(knowledge_base_ids),
            KnowledgeBase.is_deleted == False
        ).count()
        if kb_count != len(knowledge_base_ids):
            return error_response("部分知识库不存在")
    
    try:
        # 获取知识库的RAGFlow ID
        ragflow_kb_ids = []
        if knowledge_base_ids:
            kbs = KnowledgeBase.query.filter(
                KnowledgeBase.id.in_(knowledge_base_ids),
                KnowledgeBase.is_deleted == False
            ).all()
            ragflow_kb_ids = [kb.ragflow_kb_id for kb in kbs if kb.ragflow_kb_id]
        
        # 在RAGFlow中创建聊天助手
        ragflow_data = ragflow_client.create_chat(
            name=name,
            dataset_ids=ragflow_kb_ids,
            llm=data.get('llm_config', {}),
            prompt=data.get('prompt', ''),
            **data.get('retrieval_config', {})
        )
        
        # 在本地数据库中创建记录
        assistant = ChatAssistant(
            name=name,
            description=description,
            ragflow_assistant_id=ragflow_data.get('id'),
            prompt=data.get('prompt', ''),
            llm_config=data.get('llm_config', {}),
            retrieval_config=data.get('retrieval_config', {}),
            knowledge_base_ids=knowledge_base_ids,
            avatar=data.get('avatar', ''),
            status='active'
        )
        assistant.save()
        
        logger.info(f"Created chat assistant: {name} (ID: {assistant.id})")
        return success_response(assistant.to_dict(), "聊天助手创建成功")
        
    except Exception as e:
        logger.error(f"Failed to create chat assistant: {str(e)}")
        return error_response(f"创建聊天助手失败: {str(e)}")

@api_bp.route('/chat-assistants/<assistant_id>', methods=['GET'])
@login_required
def get_chat_assistant(assistant_id):
    """获取聊天助手详情"""
    assistant = ChatAssistant.get_by_id(assistant_id)
    if not assistant:
        return error_response("聊天助手不存在", 404)
    
    try:
        # 从RAGFlow获取最新信息
        if assistant.ragflow_assistant_id:
            ragflow_data = ragflow_client.get_chat(assistant.ragflow_assistant_id)
            # 可以更新一些统计信息
            # assistant.conversation_count = ragflow_data.get('conversation_count', 0)
            # assistant.save()
        
        return success_response(assistant.to_dict())
        
    except Exception as e:
        logger.warning(f"Failed to sync chat assistant from RAGFlow: {str(e)}")
        return success_response(assistant.to_dict())

@api_bp.route('/chat-assistants/<assistant_id>', methods=['PUT'])
@login_required
def update_chat_assistant(assistant_id):
    """更新聊天助手"""
    assistant = ChatAssistant.get_by_id(assistant_id)
    if not assistant:
        return error_response("聊天助手不存在", 404)
    
    data = request.get_json() or {}
    
    try:
        # 验证知识库ID
        if 'knowledge_base_ids' in data:
            knowledge_base_ids = data['knowledge_base_ids']
            if knowledge_base_ids:
                kb_count = KnowledgeBase.query.filter(
                    KnowledgeBase.id.in_(knowledge_base_ids),
                    KnowledgeBase.is_deleted == False
                ).count()
                if kb_count != len(knowledge_base_ids):
                    return error_response("部分知识库不存在")
        
        # 更新RAGFlow中的聊天助手
        if assistant.ragflow_assistant_id:
            update_data = {}
            if 'name' in data:
                update_data['name'] = data['name']
            if 'prompt' in data:
                update_data['prompt'] = data['prompt']
            if 'llm_config' in data:
                update_data['llm'] = data['llm_config']
            if 'retrieval_config' in data:
                update_data.update(data['retrieval_config'])
            
            # 更新关联的知识库
            if 'knowledge_base_ids' in data:
                kbs = KnowledgeBase.query.filter(
                    KnowledgeBase.id.in_(data['knowledge_base_ids']),
                    KnowledgeBase.is_deleted == False
                ).all()
                ragflow_kb_ids = [kb.ragflow_kb_id for kb in kbs if kb.ragflow_kb_id]
                update_data['dataset_ids'] = ragflow_kb_ids
            
            if update_data:
                ragflow_client.update_chat(assistant.ragflow_assistant_id, **update_data)
        
        # 更新本地数据库
        updatable_fields = [
            'name', 'description', 'prompt', 'llm_config', 
            'retrieval_config', 'knowledge_base_ids', 'avatar', 'status'
        ]
        for field in updatable_fields:
            if field in data:
                setattr(assistant, field, data[field])
        
        assistant.save()
        
        logger.info(f"Updated chat assistant: {assistant.name} (ID: {assistant.id})")
        return success_response(assistant.to_dict(), "聊天助手更新成功")
        
    except Exception as e:
        logger.error(f"Failed to update chat assistant: {str(e)}")
        return error_response(f"更新聊天助手失败: {str(e)}")

@api_bp.route('/chat-assistants/<assistant_id>', methods=['DELETE'])
@login_required
def delete_chat_assistant(assistant_id):
    """删除聊天助手"""
    assistant = ChatAssistant.get_by_id(assistant_id)
    if not assistant:
        return error_response("聊天助手不存在", 404)
    
    try:
        # 从RAGFlow中删除聊天助手
        if assistant.ragflow_assistant_id:
            ragflow_client.delete_chat(assistant.ragflow_assistant_id)
        
        # 软删除本地记录
        assistant.delete()
        
        logger.info(f"Deleted chat assistant: {assistant.name} (ID: {assistant.id})")
        return success_response(message="聊天助手删除成功")
        
    except Exception as e:
        logger.error(f"Failed to delete chat assistant: {str(e)}")
        return error_response(f"删除聊天助手失败: {str(e)}")

@api_bp.route('/chat-assistants/<assistant_id>/test', methods=['POST'])
@login_required
@validate_json('message')
def test_chat_assistant(assistant_id):
    """测试聊天助手"""
    assistant = ChatAssistant.get_by_id(assistant_id)
    if not assistant:
        return error_response("聊天助手不存在", 404)
    
    if not assistant.ragflow_assistant_id:
        return error_response("聊天助手未关联RAGFlow")
    
    data = request.get_json()
    message = data['message']
    
    try:
        # 创建临时对话
        conversation_data = ragflow_client.create_conversation(
            chat_id=assistant.ragflow_assistant_id,
            name="测试对话"
        )
        
        # 发送消息
        response_data = ragflow_client.send_message(
            conversation_id=conversation_data['id'],
            message=message,
            stream=False
        )
        
        return success_response({
            'message': message,
            'response': response_data.get('answer', ''),
            'references': response_data.get('reference', [])
        }, "测试成功")
        
    except Exception as e:
        logger.error(f"Failed to test chat assistant: {str(e)}")
        return error_response(f"测试失败: {str(e)}")

@api_bp.route('/chat-assistants/<assistant_id>/statistics', methods=['GET'])
@login_required
def get_chat_assistant_statistics(assistant_id):
    """获取聊天助手统计信息"""
    assistant = ChatAssistant.get_by_id(assistant_id)
    if not assistant:
        return error_response("聊天助手不存在", 404)
    
    try:
        # 获取对话统计
        from app.models.conversation import Conversation, Message
        conversations = Conversation.query.filter_by(
            assistant_id=assistant_id, 
            is_deleted=False
        ).all()
        
        total_messages = 0
        for conv in conversations:
            total_messages += Message.query.filter_by(
                conversation_id=conv.id,
                is_deleted=False
            ).count()
        
        stats = {
            'total_conversations': len(conversations),
            'active_conversations': len([c for c in conversations if c.status == 'active']),
            'total_messages': total_messages,
            'avg_messages_per_conversation': total_messages / len(conversations) if conversations else 0,
            'knowledge_bases': len(assistant.knowledge_base_ids or [])
        }
        
        return success_response(stats)
        
    except Exception as e:
        logger.error(f"Failed to get chat assistant statistics: {str(e)}")
        return error_response(f"获取统计信息失败: {str(e)}")
