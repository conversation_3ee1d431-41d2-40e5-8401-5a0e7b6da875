@echo off
chcp 65001 >nul

echo ========================================
echo   知识库管理平台 - 前端开发服务器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或未添加到PATH
    echo 请安装Python 3.6+并添加到系统PATH
    pause
    exit /b 1
)

echo 正在启动开发服务器...
echo 服务地址: http://localhost:3000
echo 按 Ctrl+C 停止服务器
echo.

REM 启动Python开发服务器
python start-dev.py

pause
