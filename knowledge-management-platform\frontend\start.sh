#!/bin/bash

echo "========================================"
echo "  知识库管理平台 - 前端开发服务器"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "错误: Python未安装"
    echo "请安装Python 3.6+并添加到系统PATH"
    exit 1
fi

# 使用python3或python命令
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

echo "正在启动开发服务器..."
echo "服务地址: http://localhost:3000"
echo "按 Ctrl+C 停止服务器"
echo

# 启动Python开发服务器
$PYTHON_CMD start-dev.py
