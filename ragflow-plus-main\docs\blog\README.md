# 开发博客

我的微信公众号[我有一计]记录了开发本项目的开发历程：

1. Ragflow技术栈分析及二次开发指南  
2. 【Ragflow】2. rag检索原理和效率解析  
3. 【Ragflow】3. 给聊天界面打个美化补丁  
4. 【Ragflow】4. 增加文档撰写功能，实现全新交互模式  
5. 【Ragflow】5. 看完Python API文档，竟然成为了官方仓库的Contributor  
6. 【Ragflow】6. Ragflow-plus重磅更新：增加用户后台管理系统  
7. 【Ragflow】7. Ragflow-plus和Ragflow有什么关系？主流问题Q&A  
8. 【Ragflow】8. 基于ragflow API 搭建极简聊天Web界面  
9. 【Ragflow】9. 问答为什么比搜索响应慢？从源码角度深入分析  
10. 【Ragflow】10. 助理配置参数详细解析 / 模型响应加速方法  
11. 【Ragflow】11. 文件解析流程分析 / 批量解析实现  
12. 【Ragflow】12. Ragflow-Plus管理系统v0.1.1：增加团队管理和用户配置功能  
13. 【Ragflow】13. Deepdoc效果一言难尽，MinerU解析降维打击  
14. 【Ragflow】14. MinerU解析脚本，接入ragflow知识库  
15. 【Ragflow】15. Ragflow-Plus管理系统v0.1.2：小升级，连夜修复若干问题  
16. 【Ragflow】16. Ragflow-Plus管理系统开发日志：重塑文件管理单元  
17. 【Ragflow】17. Ragflow-Plus开发日志：增加知识库管理功能 / 支持MinerU解析 / 图片存储与读取  
18. 【Ragflow】18. 更好的推理框架：vLLM的docker部署方式  
19. 【Ragflow】19. RagflowPlus(v0.2.0)：完善MinerU解析 / 支持图文关联输出  
20. 【Ragflow】20. Ragflow-Plus项目简介与操作指南  
21. 【Ragflow】21. RagflowPlus(v0.2.1)：6个bug修复 / 增加重置密码功能  
22. 【Ragflow】22. RagflowPlus(v0.3.0)：用户会话管理 / 文件类型拓展 / 诸多优化更新  
23. 【Ragflow】23. Ragflowv0.19.0：新特性解读  
24. 【Ragflow】24. Ragflow-plus开发日志：增加分词逻辑，修复关键词检索失效问题  
25. 【Ragflow】25. Ragflow-plus开发日志：excel文件解析新思路 / 公式解析适配  
26. 【Ragflow】26. RagflowPlus(v0.4.0)：完善解析逻辑 / 文档撰写模式全新升级  
27. 【Ragflow】27. RagflowPlus(v0.4.1)：小版本迭代，问题修复与功能优化
28. 【Ragflow】28.RagflowPlus(v0.4.2)：继续修复已知问题  
29. 【Ragflow】29.RagflowPlus(v0.4.3)：遗留问题修复/项目文档完善
30. 【Ragflow】30.离线环境迁移方案
31. 【Ragflow】31.RagflowPlus(v0.5.0)：嵌入模型解禁/支持关联图像自定义修改