# 知识库管理平台 API 文档

## 基础信息

- 基础URL: `http://localhost:5000/api`
- 认证方式: <PERSON><PERSON> (JWT)
- 响应格式: JSON

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "错误信息"
}
```

### 分页响应
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [],
    "pagination": {
      "total": 100,
      "page": 1,
      "per_page": 20,
      "pages": 5,
      "has_prev": false,
      "has_next": true
    }
  }
}
```

## 认证接口

### 用户登录
- **POST** `/auth/login`
- **参数**:
  ```json
  {
    "username": "用户名或邮箱",
    "password": "密码"
  }
  ```
- **响应**:
  ```json
  {
    "access_token": "JWT令牌",
    "user": {
      "id": "用户ID",
      "username": "用户名",
      "email": "邮箱",
      "nickname": "昵称",
      "role": "角色"
    }
  }
  ```

### 获取用户信息
- **GET** `/auth/profile`
- **需要认证**: 是

### 更新用户信息
- **PUT** `/auth/profile`
- **需要认证**: 是
- **参数**:
  ```json
  {
    "nickname": "昵称",
    "phone": "手机号"
  }
  ```

## 知识库管理

### 获取知识库列表
- **GET** `/knowledge-bases`
- **需要认证**: 是
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `per_page`: 每页数量 (默认: 20, 最大: 100)
  - `search`: 搜索关键词
  - `status`: 状态筛选

### 创建知识库
- **POST** `/knowledge-bases`
- **需要认证**: 是
- **参数**:
  ```json
  {
    "name": "知识库名称",
    "description": "描述",
    "chunk_method": "分块方法",
    "parser_config": {}
  }
  ```

### 获取知识库详情
- **GET** `/knowledge-bases/{id}`
- **需要认证**: 是

### 更新知识库
- **PUT** `/knowledge-bases/{id}`
- **需要认证**: 是

### 删除知识库
- **DELETE** `/knowledge-bases/{id}`
- **需要认证**: 是

### 同步知识库
- **POST** `/knowledge-bases/{id}/sync`
- **需要认证**: 是

## 文档管理

### 获取文档列表
- **GET** `/knowledge-bases/{kb_id}/documents`
- **需要认证**: 是

### 上传文档
- **POST** `/knowledge-bases/{kb_id}/documents/upload`
- **需要认证**: 是
- **Content-Type**: `multipart/form-data`
- **参数**:
  - `file`: 文件
  - `name`: 文档名称 (可选)

### 删除文档
- **DELETE** `/documents/{id}`
- **需要认证**: 是

### 重新解析文档
- **POST** `/documents/{id}/reparse`
- **需要认证**: 是

## 聊天助手管理

### 获取助手列表
- **GET** `/chat-assistants`
- **需要认证**: 是

### 创建助手
- **POST** `/chat-assistants`
- **需要认证**: 是
- **参数**:
  ```json
  {
    "name": "助手名称",
    "description": "描述",
    "prompt": "系统提示词",
    "knowledge_base_ids": ["知识库ID列表"],
    "llm_config": {},
    "retrieval_config": {}
  }
  ```

### 测试助手
- **POST** `/chat-assistants/{id}/test`
- **需要认证**: 是
- **参数**:
  ```json
  {
    "message": "测试消息"
  }
  ```

## 敏感词管理

### 获取敏感词列表
- **GET** `/sensitive-words`
- **需要认证**: 是

### 创建敏感词
- **POST** `/sensitive-words`
- **需要认证**: 是
- **参数**:
  ```json
  {
    "word": "敏感词",
    "category": "分类",
    "level": "级别",
    "replacement": "替换词",
    "action": "处理动作"
  }
  ```

### 批量创建敏感词
- **POST** `/sensitive-words/batch`
- **需要认证**: 是
- **参数**:
  ```json
  {
    "words": ["敏感词列表"],
    "category": "默认分类",
    "level": "默认级别"
  }
  ```

### 检测敏感词
- **POST** `/sensitive-words/check`
- **需要认证**: 是
- **参数**:
  ```json
  {
    "text": "待检测文本"
  }
  ```

## 用户管理 (管理员)

### 获取用户列表
- **GET** `/users`
- **需要认证**: 是
- **需要权限**: 管理员

### 创建用户
- **POST** `/users`
- **需要认证**: 是
- **需要权限**: 管理员

### 重置用户密码
- **POST** `/users/{id}/reset-password`
- **需要认证**: 是
- **需要权限**: 管理员

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证或认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误
