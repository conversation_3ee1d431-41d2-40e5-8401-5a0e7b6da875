server {
    listen 80;
    
    client_max_body_size 500M;
    
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }

    location /v3-admin-vite/ {
        alias /usr/share/nginx/html/;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        # 将所有以/api/开头的请求转发到后端服务(management-backend容器的5000端口)
        proxy_pass http://management-backend:5000/api/;
        # 设置代理请求头
        proxy_set_header Host $host;  # 保留原始请求的Host头
        # 传递客户端真实IP
        proxy_set_header X-Real-IP $remote_addr;  # 记录客户端IP
        # 添加X-Forwarded-For头
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;  # 代理链路追踪
    }
}