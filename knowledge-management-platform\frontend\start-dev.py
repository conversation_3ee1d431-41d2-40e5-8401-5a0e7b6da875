#!/usr/bin/env python3
"""
简单的开发服务器启动脚本
用于在开发环境中快速启动前端服务
"""

import http.server
import socketserver
import os
import sys
import webbrowser
from pathlib import Path

# 配置
PORT = 3000
HOST = 'localhost'

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_OPTIONS(self):
        # 处理预检请求
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # 自定义日志格式
        print(f"[{self.log_date_time_string()}] {format % args}")

def main():
    # 确保在正确的目录中
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print(f"知识库管理平台 - 前端开发服务器")
    print(f"启动目录: {script_dir}")
    print(f"服务地址: http://{HOST}:{PORT}")
    print(f"按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        # 创建服务器
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print(f"服务器已启动在 http://{HOST}:{PORT}")
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://{HOST}:{PORT}')
                print("已自动打开浏览器")
            except Exception as e:
                print(f"无法自动打开浏览器: {e}")
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"错误: 端口 {PORT} 已被占用")
            print("请尝试以下解决方案:")
            print("1. 关闭占用端口的程序")
            print("2. 修改脚本中的PORT变量使用其他端口")
        else:
            print(f"启动服务器时出错: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"未知错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
