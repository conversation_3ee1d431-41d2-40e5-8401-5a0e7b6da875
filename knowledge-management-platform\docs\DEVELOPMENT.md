# 开发指南

## 开发环境搭建

### 前置要求

- Python 3.11+
- Node.js 16+ (如果需要使用npm工具)
- MySQL 8.0+
- Redis 6.0+
- Git

### 后端开发环境

1. **创建虚拟环境**
   ```bash
   cd backend
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # Linux/macOS
   source venv/bin/activate
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件
   ```

4. **初始化数据库**
   ```bash
   # 确保MySQL服务运行
   # 创建数据库
   mysql -u root -p -e "CREATE DATABASE knowledge_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
   ```

5. **启动开发服务器**
   ```bash
   python run.py
   ```

### 前端开发环境

前端使用纯HTML+CSS+JavaScript，无需复杂的构建工具。

1. **启动开发服务器**
   ```bash
   cd frontend
   
   # 方式1: 使用Python内置服务器
   python -m http.server 3000
   
   # 方式2: 使用live-server (需要npm)
   npm install -g live-server
   live-server --port=3000
   
   # 方式3: 使用任意HTTP服务器
   ```

2. **访问应用**
   - 前端: http://localhost:3000
   - 后端API: http://localhost:5000

## 项目结构详解

### 后端结构

```
backend/
├── app/
│   ├── __init__.py           # 应用工厂
│   ├── models/               # 数据库模型
│   │   ├── __init__.py
│   │   ├── base.py          # 基础模型类
│   │   ├── user.py          # 用户模型
│   │   ├── knowledge_base.py # 知识库模型
│   │   ├── document.py      # 文档模型
│   │   ├── chat_assistant.py # 聊天助手模型
│   │   ├── conversation.py  # 对话模型
│   │   └── sensitive_word.py # 敏感词模型
│   ├── api/                 # API路由
│   │   ├── __init__.py
│   │   ├── auth.py          # 认证接口
│   │   ├── knowledge_base.py # 知识库接口
│   │   ├── document.py      # 文档接口
│   │   ├── chat_assistant.py # 助手接口
│   │   ├── conversation.py  # 对话接口
│   │   ├── sensitive_word.py # 敏感词接口
│   │   └── user.py          # 用户接口
│   └── utils/               # 工具函数
│       ├── __init__.py
│       ├── ragflow_client.py # RAGFlow客户端
│       ├── response.py      # 响应工具
│       ├── decorators.py    # 装饰器
│       └── validators.py    # 验证器
├── config/
│   └── config.py            # 配置文件
├── requirements.txt         # Python依赖
└── run.py                   # 启动文件
```

### 前端结构

```
frontend/
├── css/
│   ├── style.css           # 主样式文件
│   └── components.css      # 组件样式
├── js/
│   ├── utils.js            # 工具函数
│   ├── api.js              # API服务
│   ├── auth.js             # 认证管理
│   ├── components.js       # 通用组件
│   ├── app.js              # 主应用控制器
│   └── pages/              # 页面逻辑
│       ├── dashboard.js
│       ├── knowledge-base.js
│       ├── chat-assistant.js
│       ├── sensitive-word.js
│       └── user-management.js
├── index.html              # 主页面
└── package.json            # 项目配置
```

## 核心功能实现

### 1. 知识库管理

**创建知识库流程:**
1. 前端调用 `POST /api/knowledge-bases`
2. 后端在RAGFlow中创建数据集
3. 本地数据库保存知识库信息
4. 返回创建结果

**文档上传流程:**
1. 前端上传文件到 `POST /api/knowledge-bases/{id}/documents/upload`
2. 后端保存文件到本地
3. 调用RAGFlow API上传文档
4. 启动文档解析
5. 保存文档记录

### 2. 聊天助手

**助手创建流程:**
1. 选择关联的知识库
2. 配置系统提示词
3. 在RAGFlow中创建聊天助手
4. 本地保存助手配置

**对话测试:**
1. 创建临时对话会话
2. 发送测试消息
3. 获取AI回复和引用信息
4. 显示对话结果

### 3. 敏感词管理

**检测流程:**
1. 接收待检测文本
2. 遍历敏感词库
3. 执行相应处理动作（替换/阻止/警告）
4. 更新命中统计
5. 返回处理结果

## API设计规范

### 请求格式
- 使用RESTful API设计
- JSON格式数据传输
- JWT Token认证

### 响应格式
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 错误处理
- 统一错误响应格式
- 详细错误信息
- 适当的HTTP状态码

## 数据库设计

### 核心表结构

1. **users** - 用户表
2. **knowledge_bases** - 知识库表
3. **documents** - 文档表
4. **chat_assistants** - 聊天助手表
5. **conversations** - 对话表
6. **messages** - 消息表
7. **sensitive_words** - 敏感词表

### 关系设计
- 知识库 1:N 文档
- 助手 1:N 对话
- 对话 1:N 消息
- 软删除设计

## 前端架构

### 单页应用设计
- 基于原生JavaScript
- 模块化页面管理
- 组件化UI设计
- 响应式布局

### 状态管理
- localStorage持久化
- 全局状态管理
- 认证状态同步

### API交互
- 统一的API服务层
- 错误处理机制
- 加载状态管理

## 开发规范

### 代码规范
- Python: PEP 8
- JavaScript: ES6+ 标准
- 统一的命名规范
- 详细的注释说明

### Git规范
- 功能分支开发
- 清晰的提交信息
- 代码审查流程

### 测试规范
- 单元测试覆盖
- API接口测试
- 前端功能测试

## 常见问题

### 1. RAGFlow连接失败
- 检查网络连接
- 验证API Key
- 确认服务器地址

### 2. 数据库连接错误
- 检查MySQL服务状态
- 验证连接配置
- 确认数据库权限

### 3. 文件上传失败
- 检查文件大小限制
- 验证文件类型
- 确认上传目录权限

### 4. 前端页面空白
- 检查JavaScript错误
- 验证API连接
- 确认认证状态

## 扩展开发

### 添加新功能模块

1. **后端开发**
   - 创建数据模型
   - 实现API接口
   - 添加业务逻辑

2. **前端开发**
   - 创建页面文件
   - 实现交互逻辑
   - 添加导航菜单

3. **集成测试**
   - API接口测试
   - 前端功能测试
   - 端到端测试

### 性能优化

1. **后端优化**
   - 数据库查询优化
   - 缓存策略
   - 异步处理

2. **前端优化**
   - 资源压缩
   - 懒加载
   - 缓存策略

## 部署建议

### 开发环境
- 使用本地数据库
- 开启调试模式
- 详细日志输出

### 生产环境
- 使用Docker部署
- 配置HTTPS
- 设置监控告警
- 定期备份数据
