from flask import jsonify
from typing import Any, Dict, Optional

def success_response(data: Any = None, message: str = "操作成功", code: int = 200) -> Dict[str, Any]:
    """成功响应"""
    response = {
        'code': code,
        'message': message,
        'success': True
    }
    if data is not None:
        response['data'] = data
    return jsonify(response)

def error_response(message: str = "操作失败", code: int = 400, data: Any = None) -> Dict[str, Any]:
    """错误响应"""
    response = {
        'code': code,
        'message': message,
        'success': False
    }
    if data is not None:
        response['data'] = data
    return jsonify(response)

def paginated_response(items: list, total: int, page: int, per_page: int, message: str = "获取成功") -> Dict[str, Any]:
    """分页响应"""
    return success_response({
        'items': items,
        'pagination': {
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page,
            'has_prev': page > 1,
            'has_next': page * per_page < total
        }
    }, message)
