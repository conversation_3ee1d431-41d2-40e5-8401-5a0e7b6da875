from app import db
from .base import BaseModel

class ChatAssistant(BaseModel):
    """聊天助手模型"""
    __tablename__ = 'chat_assistants'
    
    name = db.Column(db.String(255), nullable=False, comment='助手名称')
    description = db.Column(db.Text, comment='助手描述')
    avatar = db.Column(db.String(500), comment='头像URL')
    
    # RAGFlow相关
    ragflow_assistant_id = db.Column(db.String(255), unique=True, comment='RAGFlow助手ID')
    
    # 配置信息
    prompt = db.Column(db.Text, comment='系统提示词')
    llm_config = db.Column(db.JSON, comment='LLM配置')
    retrieval_config = db.Column(db.JSON, comment='检索配置')
    
    # 关联的知识库
    knowledge_base_ids = db.Column(db.JSON, comment='关联的知识库ID列表')
    
    # 状态
    status = db.Column(db.String(20), default='active', comment='状态: active, inactive')
    
    # 统计信息
    conversation_count = db.Column(db.Integer, default=0, comment='对话数量')
    message_count = db.Column(db.Integer, default=0, comment='消息数量')
    
    # 关联关系
    conversations = db.relationship('Conversation', backref='chat_assistant', lazy='dynamic')
    
    def __repr__(self):
        return f'<ChatAssistant {self.name}>'
    
    def to_dict(self):
        """转换为字典，包含统计信息"""
        data = super().to_dict()
        data['conversation_count'] = self.conversations.filter_by(is_deleted=False).count()
        return data
