// 用户管理页面

class UserManagementPage extends BasePage {
    constructor() {
        super()
        this.currentPage = 1
        this.pageSize = 20
        this.searchKeyword = ''
        this.roleFilter = ''
        this.statusFilter = ''
        this.data = []
        this.pagination = null
    }

    async render() {
        // 检查管理员权限
        if (!authManager.isAdmin()) {
            this.renderContent(`
                <div class="empty-state">
                    <i class="fas fa-lock"></i>
                    <h3>权限不足</h3>
                    <p>只有管理员才能访问用户管理功能</p>
                </div>
            `)
            return
        }

        const html = `
            <div class="page-header">
                <h1 class="page-title">用户管理</h1>
                <p class="page-description">管理系统用户，包括创建、编辑和删除用户账户</p>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">用户列表</h3>
                    <div class="toolbar">
                        <div class="toolbar-left">
                            ${createSearchBox('搜索用户...', 'searchUsers')}
                            <select class="form-control" id="role-filter" style="width: 120px;">
                                <option value="">全部角色</option>
                                <option value="admin">管理员</option>
                                <option value="user">普通用户</option>
                            </select>
                            <select class="form-control" id="status-filter" style="width: 120px;">
                                <option value="">全部状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">未激活</option>
                                <option value="banned">已禁用</option>
                            </select>
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-primary" onclick="showCreateUserModal()">
                                <i class="fas fa-plus"></i> 新建用户
                            </button>
                            <button class="btn" onclick="refreshUserList()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="user-table">
                        <!-- 表格内容将在这里动态加载 -->
                    </div>
                </div>
            </div>
        `
        
        this.renderContent(html)
        await this.loadData()
    }

    async loadData() {
        try {
            showLoading()
            
            const params = {
                page: this.currentPage,
                per_page: this.pageSize
            }
            
            if (this.searchKeyword) params.search = this.searchKeyword
            if (this.roleFilter) params.role = this.roleFilter
            if (this.statusFilter) params.status = this.statusFilter
            
            const result = await userAPI.list(params)
            this.data = result.items
            this.pagination = result.pagination
            
            this.renderTable()
        } catch (error) {
            showMessage(error.message || '加载失败', 'error')
        } finally {
            hideLoading()
        }
    }

    renderTable() {
        const columns = [
            {
                title: '用户信息',
                dataIndex: 'username',
                render: (value, row) => `
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: #52c41a; display: flex; align-items: center; justify-content: center; color: white;">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <strong>${escapeHtml(value)}</strong>
                            <br><small style="color: #999;">${escapeHtml(row.email)}</small>
                            ${row.nickname ? `<br><small style="color: #666;">${escapeHtml(row.nickname)}</small>` : ''}
                        </div>
                    </div>
                `
            },
            {
                title: '角色',
                dataIndex: 'role',
                width: '100px',
                render: (value) => {
                    const roleMap = {
                        'admin': { text: '管理员', class: 'danger' },
                        'user': { text: '普通用户', class: 'primary' }
                    }
                    const role = roleMap[value] || { text: value, class: 'primary' }
                    return `<span class="tag ${role.class}">${role.text}</span>`
                }
            },
            {
                title: '登录次数',
                dataIndex: 'login_count',
                width: '100px'
            },
            {
                title: '最后登录',
                dataIndex: 'last_login_at',
                width: '160px',
                render: (value) => value ? formatDate(value, 'YYYY-MM-DD HH:mm') : '从未登录'
            },
            {
                title: '状态',
                dataIndex: 'status',
                width: '80px',
                render: (value) => getStatusTag(value)
            },
            {
                title: '创建时间',
                dataIndex: 'created_at',
                width: '160px',
                render: (value) => formatDate(value, 'YYYY-MM-DD HH:mm')
            }
        ]

        const actions = [
            {
                text: '<i class="fas fa-eye"></i>',
                className: 'btn btn-sm',
                onClick: 'viewUser'
            },
            {
                text: '<i class="fas fa-edit"></i>',
                className: 'btn btn-sm',
                onClick: 'editUser'
            },
            {
                text: '<i class="fas fa-key"></i>',
                className: 'btn btn-sm btn-warning',
                onClick: 'resetUserPassword',
                disabled: (row) => row.role === 'admin'
            },
            {
                text: '<i class="fas fa-trash"></i>',
                className: 'btn btn-sm btn-danger',
                onClick: 'deleteUser',
                disabled: (row) => row.role === 'admin'
            }
        ]

        const tableHTML = createDataTable(columns, this.data, { actions })
        const paginationHTML = createPagination(this.pagination, 'changeUserPage')
        
        document.getElementById('user-table').innerHTML = tableHTML + paginationHTML
    }

    bindEvents() {
        // 绑定搜索事件
        bindSearchEvents((value) => {
            this.searchKeyword = value
            this.currentPage = 1
            this.loadData()
        })

        // 绑定筛选事件
        const filters = ['role-filter', 'status-filter']
        filters.forEach(filterId => {
            const filter = document.getElementById(filterId)
            if (filter) {
                filter.addEventListener('change', (e) => {
                    const filterType = filterId.replace('-filter', '')
                    this[filterType + 'Filter'] = e.target.value
                    this.currentPage = 1
                    this.loadData()
                })
            }
        })

        // 绑定全局函数
        window.searchUsers = (keyword) => {
            this.searchKeyword = keyword
            this.currentPage = 1
            this.loadData()
        }

        window.changeUserPage = (page) => {
            this.currentPage = page
            this.loadData()
        }

        window.refreshUserList = () => {
            this.loadData()
        }

        window.viewUser = (id) => {
            this.viewUser(id)
        }

        window.editUser = (id) => {
            this.editUser(id)
        }

        window.resetUserPassword = (id) => {
            this.resetUserPassword(id)
        }

        window.deleteUser = (id) => {
            this.deleteUser(id)
        }

        window.showCreateUserModal = () => {
            this.showCreateModal()
        }

        window.submitCreateUser = (data) => {
            return this.createUser(data)
        }

        window.submitUpdateUser = (data) => {
            return this.updateUser(this.editingId, data)
        }
    }

    // 显示创建模态框
    showCreateModal() {
        const fields = [
            { name: 'username', label: '用户名', type: 'text', required: true, placeholder: '请输入用户名' },
            { name: 'email', label: '邮箱', type: 'email', required: true, placeholder: '请输入邮箱' },
            { name: 'password', label: '密码', type: 'password', required: true, placeholder: '请输入密码' },
            { name: 'nickname', label: '昵称', type: 'text', placeholder: '请输入昵称' },
            { name: 'phone', label: '手机号', type: 'tel', placeholder: '请输入手机号' },
            { 
                name: 'role', 
                label: '角色', 
                type: 'select',
                defaultValue: 'user',
                options: [
                    { value: 'user', label: '普通用户' },
                    { value: 'admin', label: '管理员' }
                ]
            },
            {
                name: 'status',
                label: '状态',
                type: 'select',
                defaultValue: 'active',
                options: [
                    { value: 'active', label: '活跃' },
                    { value: 'inactive', label: '未激活' }
                ]
            }
        ]

        createFormModal('创建用户', fields, 'submitCreateUser')
    }

    // 创建用户
    async createUser(data) {
        const result = await userAPI.create(data)
        showMessage('用户创建成功', 'success')
        await this.loadData()
        return result
    }

    // 查看用户详情
    async viewUser(id) {
        try {
            showLoading()
            const user = await userAPI.get(id)
            
            const modalHTML = `
                <div class="modal-overlay">
                    <div class="modal" style="width: 600px;">
                        <div class="modal-header">
                            <h3 class="modal-title">用户详情 - ${escapeHtml(user.username)}</h3>
                            <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>用户ID</label>
                                <input type="text" class="form-control" value="${user.id}" readonly>
                            </div>
                            <div class="form-group">
                                <label>用户名</label>
                                <input type="text" class="form-control" value="${user.username}" readonly>
                            </div>
                            <div class="form-group">
                                <label>邮箱</label>
                                <input type="text" class="form-control" value="${user.email}" readonly>
                            </div>
                            <div class="form-group">
                                <label>昵称</label>
                                <input type="text" class="form-control" value="${user.nickname || ''}" readonly>
                            </div>
                            <div class="form-group">
                                <label>手机号</label>
                                <input type="text" class="form-control" value="${user.phone || ''}" readonly>
                            </div>
                            <div class="form-group">
                                <label>角色</label>
                                <input type="text" class="form-control" value="${user.role === 'admin' ? '管理员' : '普通用户'}" readonly>
                            </div>
                            <div class="form-group">
                                <label>登录次数</label>
                                <input type="text" class="form-control" value="${user.login_count || 0}" readonly>
                            </div>
                            <div class="form-group">
                                <label>最后登录时间</label>
                                <input type="text" class="form-control" value="${user.last_login_at ? formatDate(user.last_login_at) : '从未登录'}" readonly>
                            </div>
                            <div class="form-group">
                                <label>创建时间</label>
                                <input type="text" class="form-control" value="${formatDate(user.created_at)}" readonly>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn" onclick="this.closest('.modal-overlay').remove()">关闭</button>
                            ${user.role !== 'admin' ? `<button class="btn btn-primary" onclick="this.closest('.modal-overlay').remove(); editUser('${id}')">编辑</button>` : ''}
                        </div>
                    </div>
                </div>
            `
            
            document.getElementById('modal-container').innerHTML = modalHTML
        } catch (error) {
            showMessage(error.message || '获取详情失败', 'error')
        } finally {
            hideLoading()
        }
    }

    // 编辑用户
    async editUser(id) {
        try {
            showLoading()
            const user = await userAPI.get(id)
            
            const fields = [
                { name: 'username', label: '用户名', type: 'text', required: true },
                { name: 'email', label: '邮箱', type: 'email', required: true },
                { name: 'nickname', label: '昵称', type: 'text' },
                { name: 'phone', label: '手机号', type: 'tel' },
                { 
                    name: 'role', 
                    label: '角色', 
                    type: 'select',
                    options: [
                        { value: 'user', label: '普通用户' },
                        { value: 'admin', label: '管理员' }
                    ]
                },
                {
                    name: 'status',
                    label: '状态',
                    type: 'select',
                    options: [
                        { value: 'active', label: '活跃' },
                        { value: 'inactive', label: '未激活' },
                        { value: 'banned', label: '已禁用' }
                    ]
                }
            ]

            this.editingId = id
            createFormModal('编辑用户', fields, 'submitUpdateUser', user)
        } catch (error) {
            showMessage(error.message || '获取用户信息失败', 'error')
        } finally {
            hideLoading()
        }
    }

    // 更新用户
    async updateUser(id, data) {
        const result = await userAPI.update(id, data)
        showMessage('用户更新成功', 'success')
        await this.loadData()
        return result
    }

    // 重置用户密码
    resetUserPassword(id) {
        const user = this.data.find(item => item.id === id)
        if (!user) return

        const modalHTML = `
            <div class="modal-overlay">
                <div class="modal" style="width: 400px;">
                    <div class="modal-header">
                        <h3 class="modal-title">重置密码</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p>为用户 <strong>${escapeHtml(user.username)}</strong> 重置密码</p>
                        <div class="form-group">
                            <label>新密码 *</label>
                            <input type="password" id="new-password" class="form-control" placeholder="请输入新密码">
                            <small style="color: #999;">密码至少8位，包含大小写字母和数字</small>
                        </div>
                        <div class="form-group">
                            <label>确认密码 *</label>
                            <input type="password" id="confirm-password" class="form-control" placeholder="请再次输入新密码">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button class="btn btn-primary" onclick="submitResetPassword('${id}')">重置密码</button>
                    </div>
                </div>
            </div>
        `
        
        document.getElementById('modal-container').innerHTML = modalHTML
        
        // 绑定提交事件
        window.submitResetPassword = async (userId) => {
            const newPassword = document.getElementById('new-password').value
            const confirmPassword = document.getElementById('confirm-password').value
            
            if (!newPassword || !confirmPassword) {
                showMessage('请输入密码', 'warning')
                return
            }
            
            if (newPassword !== confirmPassword) {
                showMessage('两次输入的密码不一致', 'warning')
                return
            }
            
            // 验证密码强度
            const passwordErrors = validatePassword(newPassword)
            if (passwordErrors.length > 0) {
                showMessage('密码不符合要求: ' + passwordErrors.join('; '), 'warning')
                return
            }
            
            try {
                showLoading()
                await userAPI.resetPassword(userId, newPassword)
                showMessage('密码重置成功', 'success')
                document.querySelector('.modal-overlay').remove()
            } catch (error) {
                showMessage(error.message || '重置密码失败', 'error')
            } finally {
                hideLoading()
            }
        }
    }

    // 删除用户
    deleteUser(id) {
        const user = this.data.find(item => item.id === id)
        if (!user) return

        if (user.role === 'admin') {
            showMessage('不能删除管理员账户', 'warning')
            return
        }

        showConfirm(
            `确定要删除用户"${user.username}"吗？此操作不可恢复。`,
            async () => {
                try {
                    showLoading()
                    await userAPI.delete(id)
                    showMessage('用户删除成功', 'success')
                    await this.loadData()
                } catch (error) {
                    showMessage(error.message || '删除失败', 'error')
                } finally {
                    hideLoading()
                }
            }
        )
    }

    bindEvents() {
        // 绑定搜索事件
        bindSearchEvents((value) => {
            this.searchKeyword = value
            this.currentPage = 1
            this.loadData()
        })

        // 绑定筛选事件
        const filters = ['role-filter', 'status-filter']
        filters.forEach(filterId => {
            const filter = document.getElementById(filterId)
            if (filter) {
                filter.addEventListener('change', (e) => {
                    const filterType = filterId.replace('-filter', '')
                    this[filterType + 'Filter'] = e.target.value
                    this.currentPage = 1
                    this.loadData()
                })
            }
        })

        // 绑定全局函数
        window.searchUsers = (keyword) => {
            this.searchKeyword = keyword
            this.currentPage = 1
            this.loadData()
        }

        window.changeUserPage = (page) => {
            this.currentPage = page
            this.loadData()
        }

        window.refreshUserList = () => {
            this.loadData()
        }

        window.viewUser = (id) => {
            this.viewUser(id)
        }

        window.editUser = (id) => {
            this.editUser(id)
        }

        window.resetUserPassword = (id) => {
            this.resetUserPassword(id)
        }

        window.deleteUser = (id) => {
            this.deleteUser(id)
        }

        window.showCreateUserModal = () => {
            this.showCreateModal()
        }

        window.submitCreateUser = (data) => {
            return this.createUser(data)
        }

        window.submitUpdateUser = (data) => {
            return this.updateUser(this.editingId, data)
        }
    }

    // 显示创建模态框
    showCreateModal() {
        const fields = [
            { name: 'username', label: '用户名', type: 'text', required: true, placeholder: '请输入用户名' },
            { name: 'email', label: '邮箱', type: 'email', required: true, placeholder: '请输入邮箱' },
            { name: 'password', label: '密码', type: 'password', required: true, placeholder: '请输入密码' },
            { name: 'nickname', label: '昵称', type: 'text', placeholder: '请输入昵称' },
            { name: 'phone', label: '手机号', type: 'tel', placeholder: '请输入手机号' },
            {
                name: 'role',
                label: '角色',
                type: 'select',
                defaultValue: 'user',
                options: [
                    { value: 'user', label: '普通用户' },
                    { value: 'admin', label: '管理员' }
                ]
            }
        ]

        createFormModal('创建用户', fields, 'submitCreateUser')
    }

    // 创建用户
    async createUser(data) {
        const result = await userAPI.create(data)
        showMessage('用户创建成功', 'success')
        await this.loadData()
        return result
    }
}
