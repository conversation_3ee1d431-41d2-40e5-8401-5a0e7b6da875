from flask import request
from app.api import api_bp
from app.models.knowledge_base import KnowledgeBase
from app.utils.response import success_response, error_response, paginated_response
from app.utils.decorators import login_required, validate_json
from app.utils.ragflow_client import ragflow_client
import logging

logger = logging.getLogger(__name__)

@api_bp.route('/knowledge-bases', methods=['GET'])
@login_required
def list_knowledge_bases():
    """获取知识库列表"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '').strip()
    status = request.args.get('status', '').strip()
    
    # 构建查询
    query = KnowledgeBase.query.filter_by(is_deleted=False)
    
    if search:
        query = query.filter(KnowledgeBase.name.contains(search))
    
    if status:
        query = query.filter_by(status=status)
    
    # 分页查询
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 转换为字典
    items = [kb.to_dict() for kb in pagination.items]
    
    return paginated_response(
        items=items,
        total=pagination.total,
        page=page,
        per_page=per_page
    )

@api_bp.route('/knowledge-bases', methods=['POST'])
@login_required
@validate_json('name')
def create_knowledge_base():
    """创建知识库"""
    data = request.get_json()
    name = data['name']
    description = data.get('description', '')
    
    # 检查名称是否已存在
    if KnowledgeBase.query.filter_by(name=name, is_deleted=False).first():
        return error_response("知识库名称已存在")
    
    try:
        # 在RAGFlow中创建知识库
        ragflow_data = ragflow_client.create_dataset(
            name=name,
            description=description,
            chunk_method=data.get('chunk_method', 'naive'),
            parser_config=data.get('parser_config', {})
        )
        
        # 在本地数据库中创建记录
        kb = KnowledgeBase(
            name=name,
            description=description,
            ragflow_kb_id=ragflow_data.get('id'),
            chunk_method=data.get('chunk_method', 'naive'),
            parser_config=data.get('parser_config', {}),
            status='active'
        )
        kb.save()
        
        logger.info(f"Created knowledge base: {name} (ID: {kb.id})")
        return success_response(kb.to_dict(), "知识库创建成功")
        
    except Exception as e:
        logger.error(f"Failed to create knowledge base: {str(e)}")
        return error_response(f"创建知识库失败: {str(e)}")

@api_bp.route('/knowledge-bases/<kb_id>', methods=['GET'])
@login_required
def get_knowledge_base(kb_id):
    """获取知识库详情"""
    kb = KnowledgeBase.get_by_id(kb_id)
    if not kb:
        return error_response("知识库不存在", 404)
    
    try:
        # 从RAGFlow获取最新信息
        if kb.ragflow_kb_id:
            ragflow_data = ragflow_client.get_dataset(kb.ragflow_kb_id)
            # 更新统计信息
            kb.document_count = ragflow_data.get('document_count', 0)
            kb.chunk_count = ragflow_data.get('chunk_count', 0)
            kb.save()
        
        return success_response(kb.to_dict())
        
    except Exception as e:
        logger.warning(f"Failed to sync knowledge base from RAGFlow: {str(e)}")
        return success_response(kb.to_dict())

@api_bp.route('/knowledge-bases/<kb_id>', methods=['PUT'])
@login_required
def update_knowledge_base(kb_id):
    """更新知识库"""
    kb = KnowledgeBase.get_by_id(kb_id)
    if not kb:
        return error_response("知识库不存在", 404)
    
    data = request.get_json() or {}
    
    try:
        # 更新RAGFlow中的知识库
        if kb.ragflow_kb_id:
            update_data = {}
            if 'name' in data:
                update_data['name'] = data['name']
            if 'description' in data:
                update_data['description'] = data['description']
            if 'chunk_method' in data:
                update_data['chunk_method'] = data['chunk_method']
            if 'parser_config' in data:
                update_data['parser_config'] = data['parser_config']
            
            if update_data:
                ragflow_client.update_dataset(kb.ragflow_kb_id, **update_data)
        
        # 更新本地数据库
        updatable_fields = ['name', 'description', 'chunk_method', 'parser_config', 'status']
        for field in updatable_fields:
            if field in data:
                setattr(kb, field, data[field])
        
        kb.save()
        
        logger.info(f"Updated knowledge base: {kb.name} (ID: {kb.id})")
        return success_response(kb.to_dict(), "知识库更新成功")
        
    except Exception as e:
        logger.error(f"Failed to update knowledge base: {str(e)}")
        return error_response(f"更新知识库失败: {str(e)}")

@api_bp.route('/knowledge-bases/<kb_id>', methods=['DELETE'])
@login_required
def delete_knowledge_base(kb_id):
    """删除知识库"""
    kb = KnowledgeBase.get_by_id(kb_id)
    if not kb:
        return error_response("知识库不存在", 404)
    
    try:
        # 从RAGFlow中删除知识库
        if kb.ragflow_kb_id:
            ragflow_client.delete_dataset(kb.ragflow_kb_id)
        
        # 软删除本地记录
        kb.delete()
        
        logger.info(f"Deleted knowledge base: {kb.name} (ID: {kb.id})")
        return success_response(message="知识库删除成功")
        
    except Exception as e:
        logger.error(f"Failed to delete knowledge base: {str(e)}")
        return error_response(f"删除知识库失败: {str(e)}")

@api_bp.route('/knowledge-bases/<kb_id>/sync', methods=['POST'])
@login_required
def sync_knowledge_base(kb_id):
    """同步知识库信息"""
    kb = KnowledgeBase.get_by_id(kb_id)
    if not kb:
        return error_response("知识库不存在", 404)
    
    if not kb.ragflow_kb_id:
        return error_response("知识库未关联RAGFlow")
    
    try:
        # 从RAGFlow获取最新信息
        ragflow_data = ragflow_client.get_dataset(kb.ragflow_kb_id)
        
        # 更新本地信息
        kb.document_count = ragflow_data.get('document_count', 0)
        kb.chunk_count = ragflow_data.get('chunk_count', 0)
        kb.save()
        
        logger.info(f"Synced knowledge base: {kb.name} (ID: {kb.id})")
        return success_response(kb.to_dict(), "同步成功")
        
    except Exception as e:
        logger.error(f"Failed to sync knowledge base: {str(e)}")
        return error_response(f"同步失败: {str(e)}")

@api_bp.route('/knowledge-bases/<kb_id>/statistics', methods=['GET'])
@login_required
def get_knowledge_base_statistics(kb_id):
    """获取知识库统计信息"""
    kb = KnowledgeBase.get_by_id(kb_id)
    if not kb:
        return error_response("知识库不存在", 404)
    
    try:
        # 获取文档统计
        from app.models.document import Document
        documents = Document.query.filter_by(knowledge_base_id=kb_id, is_deleted=False).all()
        
        stats = {
            'total_documents': len(documents),
            'parsing_documents': len([d for d in documents if d.parse_status == 'processing']),
            'completed_documents': len([d for d in documents if d.parse_status == 'completed']),
            'failed_documents': len([d for d in documents if d.parse_status == 'failed']),
            'total_chunks': sum(d.chunk_count or 0 for d in documents),
            'total_tokens': sum(d.token_count or 0 for d in documents),
            'file_types': {}
        }
        
        # 统计文件类型
        for doc in documents:
            file_type = doc.file_type or 'unknown'
            stats['file_types'][file_type] = stats['file_types'].get(file_type, 0) + 1
        
        return success_response(stats)
        
    except Exception as e:
        logger.error(f"Failed to get knowledge base statistics: {str(e)}")
        return error_response(f"获取统计信息失败: {str(e)}")
