#!/bin/bash

# 知识库管理平台启动脚本

echo "=== 知识库管理平台启动脚本 ==="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 进入项目目录
cd "$(dirname "$0")/.."

# 检查配置文件
if [ ! -f "docker/.env" ]; then
    echo "创建配置文件..."
    cp docker/.env.example docker/.env
    echo "请编辑 docker/.env 文件配置相关参数"
fi

# 创建必要的目录
mkdir -p docker/uploads
mkdir -p docker/logs
mkdir -p docker/mysql

echo "启动服务..."

# 启动Docker Compose
cd docker
docker-compose up -d

echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

echo ""
echo "=== 启动完成 ==="
echo "前端地址: http://localhost:3000"
echo "后端API: http://localhost:5000"
echo "默认管理员账户: admin / admin123456"
echo ""
echo "查看日志: docker-compose logs -f"
echo "停止服务: docker-compose down"
