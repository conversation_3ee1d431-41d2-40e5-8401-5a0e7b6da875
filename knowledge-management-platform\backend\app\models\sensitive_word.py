from app import db
from .base import BaseModel

class SensitiveWord(BaseModel):
    """敏感词模型"""
    __tablename__ = 'sensitive_words'
    
    word = db.Column(db.String(255), nullable=False, unique=True, comment='敏感词')
    category = db.Column(db.String(100), comment='分类')
    level = db.Column(db.String(20), default='medium', comment='敏感级别: low, medium, high')
    
    # 替换配置
    replacement = db.Column(db.String(255), comment='替换词')
    action = db.Column(db.String(20), default='replace', comment='处理动作: replace, block, warn')
    
    # 状态
    status = db.Column(db.String(20), default='active', comment='状态: active, inactive')
    
    # 统计信息
    hit_count = db.Column(db.Integer, default=0, comment='命中次数')
    last_hit_at = db.Column(db.DateTime, comment='最后命中时间')
    
    def __repr__(self):
        return f'<SensitiveWord {self.word}>'

class SensitiveWordRule(BaseModel):
    """敏感词规则模型"""
    __tablename__ = 'sensitive_word_rules'
    
    name = db.Column(db.String(255), nullable=False, comment='规则名称')
    description = db.Column(db.Text, comment='规则描述')
    
    # 规则配置
    pattern = db.Column(db.Text, comment='正则表达式模式')
    categories = db.Column(db.JSON, comment='适用的敏感词分类')
    levels = db.Column(db.JSON, comment='适用的敏感级别')
    
    # 应用范围
    apply_to_input = db.Column(db.Boolean, default=True, comment='应用到输入')
    apply_to_output = db.Column(db.Boolean, default=True, comment='应用到输出')
    
    # 状态
    status = db.Column(db.String(20), default='active', comment='状态: active, inactive')
    priority = db.Column(db.Integer, default=0, comment='优先级')
    
    def __repr__(self):
        return f'<SensitiveWordRule {self.name}>'
