<div align="center">
  <img src="images/ragflow-plus.png" width="400" alt="Ragflow-Plus">
</div>

<div align="center">
  <img src="https://img.shields.io/badge/版本-0.5.0-blue" alt="版本">
  <a href="LICENSE"><img src="https://img.shields.io/badge/许可证-AGPL3.0-green" alt="许可证"></a>
</div>


---

## 🌟 简介

Ragflow-Plus 是一个基于 Ragflow 的二次开发项目，目的是解决实际应用中的一些问题，主要有以下特点：

- 管理模式  
额外搭建后台管理系统，支持管理员执行用户管理、团队管理、配置管理、文件管理、知识库管理等功能
- 权限回收  
前台系统对用户权限进行收缩，进一步简化界面
- 解析增强  
使用MinerU替代DeepDoc算法，使文件解析效果更好，并支持图片解析
- 图文输出  
支持模型在回答时，输出引用文本块关联的相关图片
- 文档撰写模式  
支持全新的文档模式交互体验

视频演示：https://www.bilibili.com/video/BV1UJLezaEEE
