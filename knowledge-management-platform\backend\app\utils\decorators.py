from functools import wraps
from flask import request, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.user import User
from app.utils.response import error_response

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        try:
            current_user_id = get_jwt_identity()
            current_user = User.get_by_id(current_user_id)
            
            if not current_user or current_user.status != 'active':
                return error_response("用户不存在或已被禁用", 401)
            
            # 将当前用户添加到请求上下文
            request.current_user = current_user
            return f(*args, **kwargs)
            
        except Exception as e:
            return error_response("认证失败", 401)
    
    return decorated_function

def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if not request.current_user.is_admin:
            return error_response("需要管理员权限", 403)
        return f(*args, **kwargs)
    
    return decorated_function

def validate_json(*required_fields):
    """JSON数据验证装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return error_response("请求必须是JSON格式", 400)
            
            data = request.get_json()
            if not data:
                return error_response("请求数据不能为空", 400)
            
            # 验证必需字段
            missing_fields = []
            for field in required_fields:
                if field not in data or data[field] is None or data[field] == '':
                    missing_fields.append(field)
            
            if missing_fields:
                return error_response(f"缺少必需字段: {', '.join(missing_fields)}", 400)
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator
