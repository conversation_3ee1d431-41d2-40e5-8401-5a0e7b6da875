# 项目启动检查清单

## 🚀 快速启动检查

### 1. 环境准备
- [ ] Docker 和 Docker Compose 已安装
- [ ] 项目代码已下载
- [ ] 配置文件已设置 (docker/.env)

### 2. 启动服务
- [ ] 执行启动脚本 (`scripts/start.sh` 或 `scripts/start.bat`)
- [ ] 所有Docker容器正常运行
- [ ] 数据库初始化完成

### 3. 访问验证
- [ ] 前端页面可访问 (http://localhost:3000)
- [ ] 后端API可访问 (http://localhost:5000/api)
- [ ] 默认管理员账户可登录 (admin/admin123456)

### 4. 功能测试
- [ ] 知识库创建功能正常
- [ ] 文档上传功能正常
- [ ] 聊天助手创建正常
- [ ] 敏感词管理正常

## 📋 详细检查项目

### 后端服务检查

#### 1. 服务启动
```bash
# 检查后端容器状态
docker-compose ps backend

# 查看后端日志
docker-compose logs backend
```

#### 2. 数据库连接
```bash
# 检查MySQL容器
docker-compose ps mysql

# 测试数据库连接
docker-compose exec mysql mysql -u root -p -e "SHOW DATABASES;"
```

#### 3. Redis连接
```bash
# 检查Redis容器
docker-compose ps redis

# 测试Redis连接
docker-compose exec redis redis-cli ping
```

#### 4. RAGFlow连接
```bash
# 测试RAGFlow API连接
curl -H "Authorization: Bearer ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm" \
     http://**************:180/api/v1/datasets
```

### 前端服务检查

#### 1. 静态文件服务
- [ ] index.html 可正常访问
- [ ] CSS文件加载正常
- [ ] JavaScript文件加载正常
- [ ] 图标库加载正常

#### 2. API代理
- [ ] /api/* 请求正确代理到后端
- [ ] 跨域配置正确
- [ ] 认证头传递正常

### 功能模块检查

#### 1. 用户认证
- [ ] 登录页面显示正常
- [ ] 用户名/密码验证
- [ ] JWT Token生成和验证
- [ ] 登录状态持久化
- [ ] 权限控制正常

#### 2. 知识库管理
- [ ] 知识库列表加载
- [ ] 创建知识库功能
- [ ] 知识库详情查看
- [ ] 知识库编辑功能
- [ ] 知识库删除功能
- [ ] RAGFlow同步功能

#### 3. 文档管理
- [ ] 文档列表显示
- [ ] 文件上传功能
- [ ] 文档解析状态
- [ ] 文档删除功能
- [ ] 重新解析功能

#### 4. 聊天助手
- [ ] 助手列表显示
- [ ] 创建助手功能
- [ ] 助手配置编辑
- [ ] 对话测试功能
- [ ] 助手删除功能

#### 5. 敏感词管理
- [ ] 敏感词列表显示
- [ ] 单个敏感词创建
- [ ] 批量导入功能
- [ ] 敏感词编辑
- [ ] 敏感词删除
- [ ] 敏感词检测API

#### 6. 用户管理 (管理员)
- [ ] 用户列表显示
- [ ] 创建用户功能
- [ ] 用户信息编辑
- [ ] 密码重置功能
- [ ] 用户删除功能

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 后端启动失败
**症状**: 后端容器无法启动或频繁重启

**检查步骤**:
```bash
# 查看详细错误日志
docker-compose logs backend

# 检查配置文件
cat docker/.env

# 检查数据库连接
docker-compose exec backend python -c "from app import create_app; app = create_app(); print('OK')"
```

**常见原因**:
- 数据库连接配置错误
- 环境变量配置错误
- 依赖包安装失败
- 端口冲突

#### 2. 前端页面空白
**症状**: 访问前端页面显示空白或加载失败

**检查步骤**:
```bash
# 检查前端容器状态
docker-compose ps frontend

# 检查nginx配置
docker-compose exec frontend cat /etc/nginx/conf.d/default.conf

# 查看浏览器控制台错误
```

**常见原因**:
- JavaScript文件加载失败
- API代理配置错误
- 认证状态异常
- 网络连接问题

#### 3. RAGFlow连接失败
**症状**: 知识库操作失败，提示RAGFlow连接错误

**检查步骤**:
```bash
# 测试网络连通性
curl http://**************:180

# 验证API Key
curl -H "Authorization: Bearer ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm" \
     http://**************:180/api/v1/datasets
```

**解决方案**:
- 检查网络连接
- 验证API Key有效性
- 确认RAGFlow服务状态

#### 4. 文件上传失败
**症状**: 文档上传时出现错误

**检查步骤**:
```bash
# 检查上传目录权限
ls -la docker/uploads/

# 检查文件大小限制
grep MAX_CONTENT_LENGTH backend/.env

# 查看上传错误日志
docker-compose logs backend | grep upload
```

**解决方案**:
- 调整文件大小限制
- 检查磁盘空间
- 验证文件格式支持

## 🧪 测试指南

### 单元测试
```bash
cd backend
python -m pytest tests/
```

### API测试
```bash
# 使用curl测试API
curl -X POST http://localhost:5000/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123456"}'
```

### 前端测试
- 打开浏览器开发者工具
- 检查网络请求
- 验证页面功能

## 📝 开发流程

### 1. 功能开发
1. 创建功能分支
2. 实现后端API
3. 实现前端界面
4. 编写测试用例
5. 提交代码审查

### 2. 代码规范
- 遵循PEP 8 (Python)
- 使用ESLint (JavaScript)
- 统一的注释规范
- 清晰的变量命名

### 3. 版本管理
- 语义化版本号
- 详细的变更日志
- 标签管理
- 发布流程

## 🔍 监控和维护

### 日志管理
```bash
# 查看应用日志
tail -f docker/logs/app.log

# 查看访问日志
docker-compose logs nginx

# 查看系统日志
docker-compose logs
```

### 性能监控
- 数据库查询性能
- API响应时间
- 内存使用情况
- 磁盘空间监控

### 定期维护
- 数据库备份
- 日志清理
- 依赖更新
- 安全补丁
