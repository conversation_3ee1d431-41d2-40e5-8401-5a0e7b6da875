ragflow:
  host: 0.0.0.0
  http_port: 9380
mysql:
  name: 'rag_flow'
  user: 'root'
  password: 'infini_rag_flow'
  host: 'localhost'
  port: 5455
  max_connections: 100
  stale_timeout: 30
minio:
  user: 'rag_flow'
  password: 'infini_rag_flow'
  host: 'localhost:9000'
es:
  hosts: 'http://localhost:1200'
  username: 'elastic'
  password: 'infini_rag_flow'
infinity:
  uri: 'localhost:23817'
  db_name: 'default_db'
redis:
  db: 1
  password: 'infini_rag_flow'
  host: 'localhost:6379'

# postgres:
#   name: 'rag_flow'
#   user: 'rag_flow'
#   password: 'infini_rag_flow'
#   host: 'postgres'
#   port: 5432
#   max_connections: 100
#   stale_timeout: 30
# s3:
#   access_key: 'access_key'
#   secret_key: 'secret_key'
#   region: 'region'
# oss:
#   access_key: 'access_key'
#   secret_key: 'secret_key'
#   endpoint_url: 'http://oss-cn-hangzhou.aliyuncs.com'
#   region: 'cn-hangzhou'
#   bucket: 'bucket_name'
# azure:
#   auth_type: 'sas'
#   container_url: 'container_url'
#   sas_token: 'sas_token'
# azure:
#   auth_type: 'spn'
#   account_url: 'account_url'
#   client_id: 'client_id'
#   secret: 'secret'
#   tenant_id: 'tenant_id'
#   container_name: 'container_name'
# user_default_llm:
#   factory: 'Tongyi-Qianwen'
#   api_key: 'sk-xxxxxxxxxxxxx'
#   base_url: ''
# oauth:
#   github:
#     client_id: xxxxxxxxxxxxxxxxxxxxxxxxx
#     secret_key: xxxxxxxxxxxxxxxxxxxxxxxxxxxx
#     url: https://github.com/login/oauth/access_token
#   feishu:
#     app_id: cli_xxxxxxxxxxxxxxxxxxx
#     app_secret: xxxxxxxxxxxxxxxxxxxxxxxxxxxx
#     app_access_token_url: https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal
#     user_access_token_url: https://open.feishu.cn/open-apis/authen/v1/oidc/access_token
#     grant_type: 'authorization_code'
# authentication:
#   client:
#     switch: false
#     http_app_key:
#     http_secret_key:
#   site:
#     switch: false
# permission:
#   switch: false
#   component: false
#   dataset: false
