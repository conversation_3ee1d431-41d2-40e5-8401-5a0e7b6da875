<!DOCTYPE html>
<html lang="zh-cn">

<head>
  <meta charset="UTF-8">
  <title>Ragflow-Plus</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <link rel="icon" href="images/favicon.ico" type="image/x-icon">
  <meta name="description" content="Description">
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@latest/lib/themes/vue.css">
  <link rel="stylesheet" href="custom.css">
</head>

<body>
  <div id="app">加载中...</div>
  <script src="//cdn.jsdelivr.net/npm/mermaid@8.0.0-rc.8/dist/mermaid.min.js"></script>
  <script>
    window.$docsify = {
      name: 'Ragflow-Plus',
      repo: 'https://github.com/zstar1003/ragflow-plus',
      loadSidebar: '_sidebar.md', 
      auto2top: true,
      subMaxLevel: 2,
      pagination: {
          previousText: '上一节',
          nextText: '下一节',
      },
      count: {
          countable: true,
          fontsize: '0.9em',
          color: 'rgb(90,90,90)',
          language: 'chinese'
      }
}
  </script>
  <!-- Put them above docsify.min.js -->
  <script src="//cdn.jsdelivr.net/npm/docsify@latest/lib/docsify.min.js"></script>
  <!-- code render-->
  <script src="//cdn.jsdelivr.net/npm/prismjs@latest/components/prism-bash.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@latest/components/prism-python.js"></script>
  <script src="//cdn.jsdelivr.net/npm/docsify-pagination@latest/dist/docsify-pagination.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/docsify-copy-code"></script>

  <script src="https://cdn.jsdelivr.net/npm/katex@latest/dist/katex.min.js"></script>
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/katex@latest/dist/katex.min.css" />
  <script src="https://cdn.jsdelivr.net/npm/marked@3"></script>
  <!-- CDN files for docsify-katex -->
  <script src="//cdn.jsdelivr.net/npm/docsify-katex@latest/dist/docsify-katex.js"></script>
  <!-- 字数统计 -->
  <script src="//unpkg.com/docsify-count/dist/countable.js"></script>
</body>

</html>