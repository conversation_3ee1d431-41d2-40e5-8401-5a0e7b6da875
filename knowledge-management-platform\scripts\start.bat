@echo off
chcp 65001 >nul

echo === 知识库管理平台启动脚本 ===

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\.."

REM 检查配置文件
if not exist "docker\.env" (
    echo 创建配置文件...
    copy "docker\.env.example" "docker\.env"
    echo 请编辑 docker\.env 文件配置相关参数
)

REM 创建必要的目录
if not exist "docker\uploads" mkdir "docker\uploads"
if not exist "docker\logs" mkdir "docker\logs"
if not exist "docker\mysql" mkdir "docker\mysql"

echo 启动服务...

REM 启动Docker Compose
cd docker
docker-compose up -d

echo 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 检查服务状态...
docker-compose ps

echo.
echo === 启动完成 ===
echo 前端地址: http://localhost:3000
echo 后端API: http://localhost:5000
echo 默认管理员账户: admin / admin123456
echo.
echo 查看日志: docker-compose logs -f
echo 停止服务: docker-compose down
echo.
pause
