# Python 3.13 兼容版本的依赖包
# 如果使用Python 3.13，请使用此文件安装依赖: pip install -r requirements-py313.txt

# Flask 核心框架 - 兼容Python 3.13
Flask==2.3.3
Flask-CORS==4.0.0
Flask-SQLAlchemy==2.5.1
Flask-Migrate==3.1.0
Flask-JWT-Extended==4.5.3

# 数据库 - SQLAlchemy 1.4版本兼容Python 3.13
SQLAlchemy==1.4.53
PyMySQL==1.1.0
redis==5.0.1

# HTTP请求
requests==2.31.0

# 环境变量管理
python-dotenv==1.0.0

# WSGI服务器
gunicorn==21.2.0

# 工具库
Werkzeug==2.3.7

# 日期时间处理
python-dateutil==2.8.2

# 加密库
cryptography==41.0.7

# JSON处理
jsonschema==4.19.2

# 文件处理
python-magic==0.4.27

# 配置管理
configparser==6.0.0

# 日志处理
colorlog==6.7.0
