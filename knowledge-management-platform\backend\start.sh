#!/bin/bash

echo "========================================"
echo "  知识库管理平台 - 后端服务启动"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "错误: Python未安装"
    echo "请安装Python 3.8+并添加到系统PATH"
    exit 1
fi

# 使用python3或python命令
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

# 显示Python版本
echo "当前Python版本:"
$PYTHON_CMD --version

# 检查虚拟环境
if [ -f "venv/bin/activate" ]; then
    echo
    echo "激活虚拟环境..."
    source venv/bin/activate
else
    echo
    echo "警告: 未找到虚拟环境"
    echo "建议创建虚拟环境: python -m venv venv"
    echo
fi

# 检查依赖是否安装
$PYTHON_CMD -c "import flask" &> /dev/null
if [ $? -ne 0 ]; then
    echo "检测到依赖未安装，正在自动安装..."
    $PYTHON_CMD install_deps.py
    if [ $? -ne 0 ]; then
        echo "依赖安装失败，请手动安装"
        exit 1
    fi
else
    echo "依赖检查通过"
fi

echo
echo "正在启动后端服务..."
echo "服务地址: http://localhost:5000"
echo "按 Ctrl+C 停止服务器"
echo

# 启动应用
$PYTHON_CMD run.py
