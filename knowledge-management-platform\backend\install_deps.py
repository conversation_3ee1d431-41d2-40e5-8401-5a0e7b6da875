#!/usr/bin/env python3
"""
智能依赖安装脚本
根据Python版本自动选择合适的依赖包版本
"""

import sys
import subprocess
import os

def get_python_version():
    """获取Python版本信息"""
    return sys.version_info

def install_dependencies():
    """根据Python版本安装合适的依赖"""
    python_version = get_python_version()
    
    print(f"检测到Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 选择合适的requirements文件
    if python_version >= (3, 13):
        requirements_file = "requirements-py313.txt"
        print("使用Python 3.13兼容版本的依赖包")
    else:
        requirements_file = "requirements.txt"
        print("使用标准版本的依赖包")
    
    # 检查requirements文件是否存在
    if not os.path.exists(requirements_file):
        print(f"❌ 错误: {requirements_file} 文件不存在")
        return False
    
    try:
        print(f"正在安装依赖包 (使用 {requirements_file})...")
        
        # 升级pip
        print("升级pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        print("安装项目依赖...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_file])
        
        print("✅ 依赖安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt")
        print("3. 手动安装有问题的包")
        return False

def verify_installation():
    """验证安装是否成功"""
    print("\n验证安装...")
    
    required_packages = [
        'flask',
        'flask_sqlalchemy', 
        'flask_cors',
        'flask_migrate',
        'flask_jwt_extended',
        'sqlalchemy',
        'pymysql',
        'redis',
        'requests'
    ]
    
    failed_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n❌ 以下包安装失败: {', '.join(failed_packages)}")
        return False
    else:
        print("\n✅ 所有依赖包验证通过")
        return True

def main():
    print("=" * 60)
    print("知识库管理平台 - 智能依赖安装")
    print("=" * 60)
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 检测到虚拟环境")
    else:
        print("⚠️  警告: 未检测到虚拟环境，建议在虚拟环境中安装")
        print("创建虚拟环境: python -m venv venv")
        print("激活虚拟环境:")
        print("  Windows: venv\\Scripts\\activate")
        print("  Linux/macOS: source venv/bin/activate")
        
        choice = input("\n是否继续安装? (y/n): ").lower().strip()
        if choice not in ['y', 'yes']:
            print("安装已取消")
            return
    
    # 安装依赖
    if install_dependencies():
        # 验证安装
        if verify_installation():
            print("\n🎉 安装完成! 现在可以运行应用:")
            print("python run.py")
        else:
            print("\n❌ 安装验证失败，请检查错误信息")
    else:
        print("\n❌ 依赖安装失败")

if __name__ == "__main__":
    main()
