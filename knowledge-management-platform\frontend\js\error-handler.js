// 全局错误处理器

// 捕获未处理的JavaScript错误
window.addEventListener('error', function(event) {
    console.error('JavaScript Error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
    })
    
    // 显示用户友好的错误信息
    showMessage('页面出现错误，请刷新重试', 'error')
})

// 捕获未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled Promise Rejection:', event.reason)
    
    // 显示用户友好的错误信息
    showMessage('操作失败，请重试', 'error')
    
    // 阻止默认的控制台错误输出
    event.preventDefault()
})

// 网络错误处理
function handleNetworkError(error) {
    console.error('Network Error:', error)
    
    if (error.message.includes('Failed to fetch')) {
        showMessage('网络连接失败，请检查网络设置', 'error')
    } else if (error.message.includes('timeout')) {
        showMessage('请求超时，请重试', 'error')
    } else {
        showMessage('网络错误，请重试', 'error')
    }
}

// API错误处理
function handleApiError(error, context = '') {
    console.error('API Error:', error, 'Context:', context)
    
    if (error.status === 401) {
        showMessage('登录已过期，请重新登录', 'error')
        if (typeof authManager !== 'undefined') {
            authManager.logout()
        }
    } else if (error.status === 403) {
        showMessage('权限不足', 'error')
    } else if (error.status === 404) {
        showMessage('请求的资源不存在', 'error')
    } else if (error.status >= 500) {
        showMessage('服务器错误，请稍后重试', 'error')
    } else {
        showMessage(error.message || '操作失败', 'error')
    }
}

// 表单验证错误处理
function handleValidationError(errors, formId) {
    console.error('Validation Error:', errors)
    
    // 清除之前的错误样式
    const form = document.getElementById(formId)
    if (form) {
        const errorElements = form.querySelectorAll('.form-error')
        errorElements.forEach(el => el.remove())
        
        const errorInputs = form.querySelectorAll('.form-control.error')
        errorInputs.forEach(input => input.classList.remove('error'))
    }
    
    // 显示新的错误信息
    Object.keys(errors).forEach(field => {
        const input = form.querySelector(`[name="${field}"]`)
        if (input) {
            input.classList.add('error')
            
            const errorDiv = document.createElement('div')
            errorDiv.className = 'form-error'
            errorDiv.textContent = errors[field]
            input.parentNode.appendChild(errorDiv)
        }
    })
}

// 页面加载错误处理
function handlePageLoadError(pageName, error) {
    console.error(`Page Load Error (${pageName}):`, error)
    
    const container = document.getElementById('page-content')
    if (container) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle" style="color: #ff4d4f;"></i>
                <h3>页面加载失败</h3>
                <p>${error.message || '未知错误'}</p>
                <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
            </div>
        `
    }
}

// 文件上传错误处理
function handleUploadError(error, filename) {
    console.error('Upload Error:', error, 'File:', filename)
    
    if (error.message.includes('size')) {
        showMessage('文件太大，请选择较小的文件', 'error')
    } else if (error.message.includes('type')) {
        showMessage('不支持的文件类型', 'error')
    } else {
        showMessage(`文件上传失败: ${error.message}`, 'error')
    }
}

// 导出错误处理函数
window.errorHandler = {
    handleNetworkError,
    handleApiError,
    handleValidationError,
    handlePageLoadError,
    handleUploadError
}

console.log('Error handler initialized')
