import re
from flask import request
from app.api import api_bp
from app.models.sensitive_word import SensitiveWord, SensitiveWordRule
from app.utils.response import success_response, error_response, paginated_response
from app.utils.decorators import login_required, validate_json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

@api_bp.route('/sensitive-words', methods=['GET'])
@login_required
def list_sensitive_words():
    """获取敏感词列表"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '').strip()
    category = request.args.get('category', '').strip()
    level = request.args.get('level', '').strip()
    status = request.args.get('status', '').strip()
    
    # 构建查询
    query = SensitiveWord.query.filter_by(is_deleted=False)
    
    if search:
        query = query.filter(SensitiveWord.word.contains(search))
    
    if category:
        query = query.filter_by(category=category)
    
    if level:
        query = query.filter_by(level=level)
    
    if status:
        query = query.filter_by(status=status)
    
    # 按命中次数倒序排列
    query = query.order_by(SensitiveWord.hit_count.desc(), SensitiveWord.created_at.desc())
    
    # 分页查询
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 转换为字典
    items = [word.to_dict() for word in pagination.items]
    
    return paginated_response(
        items=items,
        total=pagination.total,
        page=page,
        per_page=per_page
    )

@api_bp.route('/sensitive-words', methods=['POST'])
@login_required
@validate_json('word')
def create_sensitive_word():
    """创建敏感词"""
    data = request.get_json()
    word = data['word'].strip()
    
    if not word:
        return error_response("敏感词不能为空")
    
    # 检查是否已存在
    if SensitiveWord.query.filter_by(word=word, is_deleted=False).first():
        return error_response("敏感词已存在")
    
    try:
        sensitive_word = SensitiveWord(
            word=word,
            category=data.get('category', ''),
            level=data.get('level', 'medium'),
            replacement=data.get('replacement', '***'),
            action=data.get('action', 'replace'),
            status='active'
        )
        sensitive_word.save()
        
        logger.info(f"Created sensitive word: {word}")
        return success_response(sensitive_word.to_dict(), "敏感词创建成功")
        
    except Exception as e:
        logger.error(f"Failed to create sensitive word: {str(e)}")
        return error_response(f"创建敏感词失败: {str(e)}")

@api_bp.route('/sensitive-words/batch', methods=['POST'])
@login_required
@validate_json('words')
def batch_create_sensitive_words():
    """批量创建敏感词"""
    data = request.get_json()
    words = data['words']
    
    if not isinstance(words, list) or not words:
        return error_response("敏感词列表不能为空")
    
    try:
        created_count = 0
        skipped_count = 0
        
        for word_data in words:
            if isinstance(word_data, str):
                word = word_data.strip()
                word_info = {
                    'word': word,
                    'category': data.get('category', ''),
                    'level': data.get('level', 'medium'),
                    'replacement': data.get('replacement', '***'),
                    'action': data.get('action', 'replace')
                }
            else:
                word = word_data.get('word', '').strip()
                word_info = word_data
            
            if not word:
                continue
            
            # 检查是否已存在
            if SensitiveWord.query.filter_by(word=word, is_deleted=False).first():
                skipped_count += 1
                continue
            
            sensitive_word = SensitiveWord(
                word=word,
                category=word_info.get('category', ''),
                level=word_info.get('level', 'medium'),
                replacement=word_info.get('replacement', '***'),
                action=word_info.get('action', 'replace'),
                status='active'
            )
            sensitive_word.save()
            created_count += 1
        
        logger.info(f"Batch created {created_count} sensitive words, skipped {skipped_count}")
        return success_response({
            'created_count': created_count,
            'skipped_count': skipped_count
        }, f"批量创建完成，成功创建{created_count}个，跳过{skipped_count}个")
        
    except Exception as e:
        logger.error(f"Failed to batch create sensitive words: {str(e)}")
        return error_response(f"批量创建敏感词失败: {str(e)}")

@api_bp.route('/sensitive-words/<word_id>', methods=['GET'])
@login_required
def get_sensitive_word(word_id):
    """获取敏感词详情"""
    word = SensitiveWord.get_by_id(word_id)
    if not word:
        return error_response("敏感词不存在", 404)
    
    return success_response(word.to_dict())

@api_bp.route('/sensitive-words/<word_id>', methods=['PUT'])
@login_required
def update_sensitive_word(word_id):
    """更新敏感词"""
    word = SensitiveWord.get_by_id(word_id)
    if not word:
        return error_response("敏感词不存在", 404)
    
    data = request.get_json() or {}
    
    # 检查词汇是否重复
    if 'word' in data and data['word'] != word.word:
        if SensitiveWord.query.filter_by(word=data['word'], is_deleted=False).first():
            return error_response("敏感词已存在")
    
    try:
        # 更新字段
        updatable_fields = ['word', 'category', 'level', 'replacement', 'action', 'status']
        for field in updatable_fields:
            if field in data:
                setattr(word, field, data[field])
        
        word.save()
        
        logger.info(f"Updated sensitive word: {word.word} (ID: {word.id})")
        return success_response(word.to_dict(), "敏感词更新成功")
        
    except Exception as e:
        logger.error(f"Failed to update sensitive word: {str(e)}")
        return error_response(f"更新敏感词失败: {str(e)}")

@api_bp.route('/sensitive-words/<word_id>', methods=['DELETE'])
@login_required
def delete_sensitive_word(word_id):
    """删除敏感词"""
    word = SensitiveWord.get_by_id(word_id)
    if not word:
        return error_response("敏感词不存在", 404)
    
    try:
        word.delete()
        
        logger.info(f"Deleted sensitive word: {word.word} (ID: {word.id})")
        return success_response(message="敏感词删除成功")
        
    except Exception as e:
        logger.error(f"Failed to delete sensitive word: {str(e)}")
        return error_response(f"删除敏感词失败: {str(e)}")

@api_bp.route('/sensitive-words/batch-delete', methods=['POST'])
@login_required
@validate_json('ids')
def batch_delete_sensitive_words():
    """批量删除敏感词"""
    data = request.get_json()
    ids = data['ids']
    
    if not isinstance(ids, list) or not ids:
        return error_response("ID列表不能为空")
    
    try:
        deleted_count = 0
        for word_id in ids:
            word = SensitiveWord.get_by_id(word_id)
            if word:
                word.delete()
                deleted_count += 1
        
        logger.info(f"Batch deleted {deleted_count} sensitive words")
        return success_response({
            'deleted_count': deleted_count
        }, f"批量删除完成，成功删除{deleted_count}个敏感词")
        
    except Exception as e:
        logger.error(f"Failed to batch delete sensitive words: {str(e)}")
        return error_response(f"批量删除敏感词失败: {str(e)}")

@api_bp.route('/sensitive-words/check', methods=['POST'])
@login_required
@validate_json('text')
def check_sensitive_words():
    """检测敏感词"""
    data = request.get_json()
    text = data['text']
    
    if not text:
        return success_response({
            'has_sensitive': False,
            'sensitive_words': [],
            'filtered_text': text
        })
    
    try:
        # 获取所有活跃的敏感词
        sensitive_words = SensitiveWord.query.filter_by(
            status='active',
            is_deleted=False
        ).all()
        
        found_words = []
        filtered_text = text
        
        for word_obj in sensitive_words:
            word = word_obj.word
            if word in text:
                found_words.append({
                    'word': word,
                    'category': word_obj.category,
                    'level': word_obj.level,
                    'action': word_obj.action,
                    'replacement': word_obj.replacement
                })
                
                # 更新命中统计
                word_obj.hit_count += 1
                word_obj.last_hit_at = datetime.utcnow()
                word_obj.save()
                
                # 根据动作处理文本
                if word_obj.action == 'replace':
                    filtered_text = filtered_text.replace(word, word_obj.replacement)
                elif word_obj.action == 'block':
                    # 阻止操作，返回错误
                    return error_response(f"内容包含敏感词: {word}", 400)
        
        result = {
            'has_sensitive': len(found_words) > 0,
            'sensitive_words': found_words,
            'filtered_text': filtered_text,
            'original_text': text
        }
        
        return success_response(result)
        
    except Exception as e:
        logger.error(f"Failed to check sensitive words: {str(e)}")
        return error_response(f"敏感词检测失败: {str(e)}")

@api_bp.route('/sensitive-words/categories', methods=['GET'])
@login_required
def get_sensitive_word_categories():
    """获取敏感词分类"""
    try:
        # 获取所有不同的分类
        from sqlalchemy import distinct
        categories = [
            row[0] for row in SensitiveWord.query.with_entities(
                distinct(SensitiveWord.category)
            ).filter(
                SensitiveWord.category.isnot(None),
                SensitiveWord.category != '',
                SensitiveWord.is_deleted == False
            ).all()
        ]
        
        return success_response(categories)
        
    except Exception as e:
        logger.error(f"Failed to get sensitive word categories: {str(e)}")
        return error_response(f"获取分类失败: {str(e)}")

@api_bp.route('/sensitive-words/statistics', methods=['GET'])
@login_required
def get_sensitive_word_statistics():
    """获取敏感词统计"""
    try:
        total_words = SensitiveWord.query.filter_by(is_deleted=False).count()
        active_words = SensitiveWord.query.filter_by(status='active', is_deleted=False).count()
        
        # 按级别统计
        level_stats = {}
        for level in ['low', 'medium', 'high']:
            count = SensitiveWord.query.filter_by(
                level=level, 
                is_deleted=False
            ).count()
            level_stats[level] = count
        
        # 按分类统计
        from sqlalchemy import func
        category_stats = dict(
            SensitiveWord.query.with_entities(
                SensitiveWord.category,
                func.count(SensitiveWord.id)
            ).filter(
                SensitiveWord.is_deleted == False,
                SensitiveWord.category.isnot(None),
                SensitiveWord.category != ''
            ).group_by(SensitiveWord.category).all()
        )
        
        # 最近命中的敏感词
        recent_hits = SensitiveWord.query.filter(
            SensitiveWord.last_hit_at.isnot(None),
            SensitiveWord.is_deleted == False
        ).order_by(SensitiveWord.last_hit_at.desc()).limit(10).all()
        
        stats = {
            'total_words': total_words,
            'active_words': active_words,
            'inactive_words': total_words - active_words,
            'level_stats': level_stats,
            'category_stats': category_stats,
            'recent_hits': [word.to_dict() for word in recent_hits]
        }
        
        return success_response(stats)
        
    except Exception as e:
        logger.error(f"Failed to get sensitive word statistics: {str(e)}")
        return error_response(f"获取统计信息失败: {str(e)}")
