#!/usr/bin/env python3
"""
Python 3.13 兼容性快速修复脚本
一键解决SQLAlchemy兼容性问题
"""

import sys
import subprocess
import os

def main():
    print("🔧 Python 3.13 兼容性快速修复")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"当前Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 13):
        print("✅ 您的Python版本无需特殊处理")
        print("请直接运行: pip install -r backend/requirements.txt")
        return
    
    print("⚠️  检测到Python 3.13，正在应用兼容性修复...")
    
    # 进入backend目录
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    if not os.path.exists(backend_dir):
        print("❌ 错误: backend目录不存在")
        return
    
    os.chdir(backend_dir)
    
    try:
        print("\n1. 卸载可能冲突的包...")
        packages_to_remove = [
            'SQLAlchemy',
            'Flask-SQLAlchemy', 
            'Flask-Migrate'
        ]
        
        for package in packages_to_remove:
            try:
                subprocess.run([sys.executable, "-m", "pip", "uninstall", package, "-y"], 
                             capture_output=True, check=False)
            except:
                pass
        
        print("2. 安装兼容版本的依赖包...")
        
        # 安装兼容版本
        compatible_packages = [
            "SQLAlchemy==1.4.53",
            "Flask-SQLAlchemy==2.5.1",
            "Flask-Migrate==3.1.0",
            "Flask==2.3.3",
            "Werkzeug==2.3.7"
        ]
        
        for package in compatible_packages:
            print(f"   安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        
        print("3. 安装其他依赖...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements-py313.txt"])
        
        print("\n✅ 修复完成!")
        print("现在可以运行: python run.py")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 修复失败: {e}")
        print("\n手动修复步骤:")
        print("1. pip uninstall SQLAlchemy Flask-SQLAlchemy -y")
        print("2. pip install SQLAlchemy==1.4.53")
        print("3. pip install Flask-SQLAlchemy==2.5.1")
        print("4. pip install -r requirements-py313.txt")
    
    except Exception as e:
        print(f"❌ 未知错误: {e}")

if __name__ == "__main__":
    main()
