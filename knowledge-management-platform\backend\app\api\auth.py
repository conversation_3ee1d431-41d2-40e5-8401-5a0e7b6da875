from flask import request
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from app.api import api_bp
from app.models.user import User
from app.utils.response import success_response, error_response
from app.utils.decorators import validate_json
from app.utils.validators import validate_email, validate_password

@api_bp.route('/auth/login', methods=['POST'])
@validate_json('username', 'password')
def login():
    """用户登录"""
    data = request.get_json()
    username = data['username']
    password = data['password']
    
    # 查找用户（支持用户名或邮箱登录）
    user = User.query.filter(
        (User.username == username) | (User.email == username),
        User.is_deleted == False,
        User.status == 'active'
    ).first()
    
    if not user or not user.check_password(password):
        return error_response("用户名或密码错误", 401)
    
    # 更新登录信息
    from datetime import datetime
    user.last_login_at = datetime.utcnow()
    user.login_count += 1
    user.save()
    
    # 生成访问令牌
    access_token = create_access_token(identity=user.id)
    
    return success_response({
        'access_token': access_token,
        'user': user.to_dict()
    }, "登录成功")

@api_bp.route('/auth/register', methods=['POST'])
@validate_json('username', 'email', 'password')
def register():
    """用户注册"""
    data = request.get_json()
    username = data['username']
    email = data['email']
    password = data['password']
    
    # 验证邮箱格式
    if not validate_email(email):
        return error_response("邮箱格式不正确")
    
    # 验证密码强度
    password_errors = validate_password(password)
    if password_errors:
        return error_response("密码不符合要求: " + "; ".join(password_errors))
    
    # 检查用户名是否已存在
    if User.query.filter_by(username=username, is_deleted=False).first():
        return error_response("用户名已存在")
    
    # 检查邮箱是否已存在
    if User.query.filter_by(email=email, is_deleted=False).first():
        return error_response("邮箱已被注册")
    
    # 创建用户
    user = User(
        username=username,
        email=email,
        nickname=data.get('nickname', username)
    )
    user.set_password(password)
    user.save()
    
    return success_response(user.to_dict(), "注册成功")

@api_bp.route('/auth/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取用户信息"""
    current_user_id = get_jwt_identity()
    user = User.get_by_id(current_user_id)
    
    if not user:
        return error_response("用户不存在", 404)
    
    return success_response(user.to_dict())

@api_bp.route('/auth/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """更新用户信息"""
    current_user_id = get_jwt_identity()
    user = User.get_by_id(current_user_id)
    
    if not user:
        return error_response("用户不存在", 404)
    
    data = request.get_json() or {}
    
    # 可更新的字段
    updatable_fields = ['nickname', 'avatar', 'phone']
    for field in updatable_fields:
        if field in data:
            setattr(user, field, data[field])
    
    user.save()
    
    return success_response(user.to_dict(), "更新成功")

@api_bp.route('/auth/change-password', methods=['POST'])
@jwt_required()
@validate_json('old_password', 'new_password')
def change_password():
    """修改密码"""
    current_user_id = get_jwt_identity()
    user = User.get_by_id(current_user_id)
    
    if not user:
        return error_response("用户不存在", 404)
    
    data = request.get_json()
    old_password = data['old_password']
    new_password = data['new_password']
    
    # 验证旧密码
    if not user.check_password(old_password):
        return error_response("原密码错误")
    
    # 验证新密码强度
    password_errors = validate_password(new_password)
    if password_errors:
        return error_response("新密码不符合要求: " + "; ".join(password_errors))
    
    # 更新密码
    user.set_password(new_password)
    user.save()
    
    return success_response(message="密码修改成功")
