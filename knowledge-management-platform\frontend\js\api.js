// API服务类

class ApiService {
    constructor() {
        this.baseURL = '/api'
        this.token = localStorage.getItem('auth_token')
    }

    // 设置认证令牌
    setToken(token) {
        this.token = token
        if (token) {
            localStorage.setItem('auth_token', token)
        } else {
            localStorage.removeItem('auth_token')
        }
    }

    // 获取请求头
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        }
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`
        }
        
        return headers
    }

    // 通用请求方法
    async request(method, url, data = null, options = {}) {
        const config = {
            method: method.toUpperCase(),
            headers: this.getHeaders(),
            ...options
        }

        if (data) {
            if (data instanceof FormData) {
                // 如果是FormData，移除Content-Type让浏览器自动设置
                delete config.headers['Content-Type']
                config.body = data
            } else {
                config.body = JSON.stringify(data)
            }
        }

        try {
            const response = await fetch(`${this.baseURL}${url}`, config)
            
            if (!response.ok) {
                if (response.status === 401) {
                    this.setToken(null)
                    window.location.href = '/login'
                    return
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`)
            }

            const result = await response.json()
            
            if (!result.success) {
                throw new Error(result.message || '请求失败')
            }

            return result.data
        } catch (error) {
            console.error('API请求失败:', error)
            throw error
        }
    }

    // GET请求
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString()
        const fullUrl = queryString ? `${url}?${queryString}` : url
        return this.request('GET', fullUrl)
    }

    // POST请求
    async post(url, data) {
        return this.request('POST', url, data)
    }

    // PUT请求
    async put(url, data) {
        return this.request('PUT', url, data)
    }

    // DELETE请求
    async delete(url) {
        return this.request('DELETE', url)
    }

    // 文件上传
    async upload(url, file, additionalData = {}) {
        const formData = new FormData()
        formData.append('file', file)
        
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key])
        })

        return this.request('POST', url, formData)
    }
}

// 创建全局API实例
const api = new ApiService()

// 认证API
const authAPI = {
    login: (username, password) => api.post('/auth/login', { username, password }),
    register: (userData) => api.post('/auth/register', userData),
    getProfile: () => api.get('/auth/profile'),
    updateProfile: (userData) => api.put('/auth/profile', userData),
    changePassword: (oldPassword, newPassword) => api.post('/auth/change-password', {
        old_password: oldPassword,
        new_password: newPassword
    })
}

// 知识库API
const knowledgeBaseAPI = {
    list: (params = {}) => api.get('/knowledge-bases', params),
    create: (data) => api.post('/knowledge-bases', data),
    get: (id) => api.get(`/knowledge-bases/${id}`),
    update: (id, data) => api.put(`/knowledge-bases/${id}`, data),
    delete: (id) => api.delete(`/knowledge-bases/${id}`),
    sync: (id) => api.post(`/knowledge-bases/${id}/sync`),
    getStatistics: (id) => api.get(`/knowledge-bases/${id}/statistics`)
}

// 文档API
const documentAPI = {
    list: (kbId, params = {}) => api.get(`/knowledge-bases/${kbId}/documents`, params),
    upload: (kbId, file, additionalData = {}) => api.upload(`/knowledge-bases/${kbId}/documents/upload`, file, additionalData),
    get: (id) => api.get(`/documents/${id}`),
    delete: (id) => api.delete(`/documents/${id}`),
    reparse: (id) => api.post(`/documents/${id}/reparse`),
    getChunks: (id, params = {}) => api.get(`/documents/${id}/chunks`, params)
}

// 聊天助手API
const chatAssistantAPI = {
    list: (params = {}) => api.get('/chat-assistants', params),
    create: (data) => api.post('/chat-assistants', data),
    get: (id) => api.get(`/chat-assistants/${id}`),
    update: (id, data) => api.put(`/chat-assistants/${id}`, data),
    delete: (id) => api.delete(`/chat-assistants/${id}`),
    test: (id, message) => api.post(`/chat-assistants/${id}/test`, { message }),
    getStatistics: (id) => api.get(`/chat-assistants/${id}/statistics`)
}

// 对话API
const conversationAPI = {
    list: (assistantId, params = {}) => api.get(`/chat-assistants/${assistantId}/conversations`, params),
    create: (assistantId, data) => api.post(`/chat-assistants/${assistantId}/conversations`, data),
    get: (id) => api.get(`/conversations/${id}`),
    update: (id, data) => api.put(`/conversations/${id}`, data),
    delete: (id) => api.delete(`/conversations/${id}`),
    getMessages: (id, params = {}) => api.get(`/conversations/${id}/messages`, params),
    sendMessage: (id, content) => api.post(`/conversations/${id}/messages`, { content }),
    clear: (id) => api.post(`/conversations/${id}/clear`),
    export: (id) => api.get(`/conversations/${id}/export`)
}

// 敏感词API
const sensitiveWordAPI = {
    list: (params = {}) => api.get('/sensitive-words', params),
    create: (data) => api.post('/sensitive-words', data),
    batchCreate: (words, commonData = {}) => api.post('/sensitive-words/batch', { words, ...commonData }),
    get: (id) => api.get(`/sensitive-words/${id}`),
    update: (id, data) => api.put(`/sensitive-words/${id}`, data),
    delete: (id) => api.delete(`/sensitive-words/${id}`),
    batchDelete: (ids) => api.post('/sensitive-words/batch-delete', { ids }),
    check: (text) => api.post('/sensitive-words/check', { text }),
    getCategories: () => api.get('/sensitive-words/categories'),
    getStatistics: () => api.get('/sensitive-words/statistics')
}

// 用户API
const userAPI = {
    list: (params = {}) => api.get('/users', params),
    create: (data) => api.post('/users', data),
    get: (id) => api.get(`/users/${id}`),
    update: (id, data) => api.put(`/users/${id}`, data),
    delete: (id) => api.delete(`/users/${id}`),
    batchDelete: (ids) => api.post('/users/batch-delete', { ids }),
    resetPassword: (id, newPassword) => api.post(`/users/${id}/reset-password`, { new_password: newPassword }),
    getStatistics: () => api.get('/users/statistics')
}
