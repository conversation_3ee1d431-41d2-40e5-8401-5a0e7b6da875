name: tests

on:
  push:
    branches:
      - 'main'
      - '*.*.*'
    paths-ignore:
      - 'docs/**'
      - '*.md'
      - '*.mdx'
  pull_request:
    types: [ opened, synchronize, reopened, labeled ]
    paths-ignore:
      - 'docs/**'
      - '*.md'
      - '*.mdx'

# https://docs.github.com/en/actions/using-jobs/using-concurrency
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  ragflow_tests:
    name: ragflow_tests
    # https://docs.github.com/en/actions/using-jobs/using-conditions-to-control-job-execution
    # https://github.com/orgs/community/discussions/26261
    if: ${{ github.event_name != 'pull_request' || contains(github.event.pull_request.labels.*.name, 'ci') }}
    runs-on: ubuntu-22.04
    steps:
      # https://github.com/hmarr/debug-action
      #- uses: hmarr/debug-action@v2

      - name: Show who triggered this workflow
        run: |
          echo "Workflow triggered by ${{ github.event_name }}"

      - name: Ensure workspace ownership
        run: echo "chown -R $USER $GITHUB_WORKSPACE" && sudo chown -R $USER $GITHUB_WORKSPACE

      # https://github.com/actions/checkout/issues/1781
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
