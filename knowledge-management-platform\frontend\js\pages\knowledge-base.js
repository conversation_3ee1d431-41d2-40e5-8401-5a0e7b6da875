// 知识库管理页面

class KnowledgeBasePage extends BasePage {
    constructor() {
        super()
        this.currentPage = 1
        this.pageSize = 20
        this.searchKeyword = ''
        this.statusFilter = ''
        this.data = []
        this.pagination = null
    }

    async render() {
        const html = `
            <div class="page-header">
                <h1 class="page-title">知识库管理</h1>
                <p class="page-description">管理您的知识库，包括创建、编辑和删除知识库</p>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">知识库列表</h3>
                    <div class="toolbar">
                        <div class="toolbar-left">
                            ${createSearchBox('搜索知识库...', 'searchKnowledgeBases')}
                            <select class="form-control" id="status-filter" style="width: 120px;">
                                <option value="">全部状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">未激活</option>
                            </select>
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-primary" onclick="showCreateKnowledgeBaseModal()">
                                <i class="fas fa-plus"></i> 新建知识库
                            </button>
                            <button class="btn" onclick="refreshKnowledgeBaseList()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="knowledge-base-table">
                        <!-- 表格内容将在这里动态加载 -->
                    </div>
                </div>
            </div>
        `
        
        this.renderContent(html)
        await this.loadData()
    }

    async loadData() {
        try {
            showLoading()
            
            const params = {
                page: this.currentPage,
                per_page: this.pageSize
            }
            
            if (this.searchKeyword) {
                params.search = this.searchKeyword
            }
            
            if (this.statusFilter) {
                params.status = this.statusFilter
            }
            
            const result = await knowledgeBaseAPI.list(params)
            this.data = result.items
            this.pagination = result.pagination
            
            this.renderTable()
        } catch (error) {
            showMessage(error.message || '加载失败', 'error')
        } finally {
            hideLoading()
        }
    }

    renderTable() {
        const columns = [
            {
                title: '知识库名称',
                dataIndex: 'name',
                render: (value, row) => `
                    <div>
                        <strong>${escapeHtml(value)}</strong>
                        ${row.description ? `<br><small style="color: #999;">${escapeHtml(row.description)}</small>` : ''}
                    </div>
                `
            },
            {
                title: '文档数量',
                dataIndex: 'document_count',
                width: '100px'
            },
            {
                title: '分块数量',
                dataIndex: 'chunk_count',
                width: '100px'
            },
            {
                title: '状态',
                dataIndex: 'status',
                width: '80px',
                render: (value) => getStatusTag(value)
            },
            {
                title: '创建时间',
                dataIndex: 'created_at',
                width: '160px',
                render: (value) => formatDate(value, 'YYYY-MM-DD HH:mm')
            }
        ]

        const actions = [
            {
                text: '<i class="fas fa-eye"></i>',
                className: 'btn btn-sm',
                onClick: 'viewKnowledgeBase'
            },
            {
                text: '<i class="fas fa-edit"></i>',
                className: 'btn btn-sm',
                onClick: 'editKnowledgeBase'
            },
            {
                text: '<i class="fas fa-sync"></i>',
                className: 'btn btn-sm',
                onClick: 'syncKnowledgeBase'
            },
            {
                text: '<i class="fas fa-trash"></i>',
                className: 'btn btn-sm btn-danger',
                onClick: 'deleteKnowledgeBase'
            }
        ]

        const tableHTML = createDataTable(columns, this.data, { actions })
        const paginationHTML = createPagination(this.pagination, 'changeKnowledgeBasePage')
        
        document.getElementById('knowledge-base-table').innerHTML = tableHTML + paginationHTML
    }

    bindEvents() {
        // 绑定搜索事件
        bindSearchEvents((value) => {
            this.searchKeyword = value
            this.currentPage = 1
            this.loadData()
        })

        // 绑定状态筛选事件
        const statusFilter = document.getElementById('status-filter')
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.statusFilter = e.target.value
                this.currentPage = 1
                this.loadData()
            })
        }

        // 绑定全局函数
        window.searchKnowledgeBases = (keyword) => {
            this.searchKeyword = keyword
            this.currentPage = 1
            this.loadData()
        }

        window.changeKnowledgeBasePage = (page) => {
            this.currentPage = page
            this.loadData()
        }

        window.refreshKnowledgeBaseList = () => {
            this.loadData()
        }

        window.viewKnowledgeBase = (id) => {
            this.viewKnowledgeBase(id)
        }

        window.editKnowledgeBase = (id) => {
            this.editKnowledgeBase(id)
        }

        window.syncKnowledgeBase = (id) => {
            this.syncKnowledgeBase(id)
        }

        window.deleteKnowledgeBase = (id) => {
            this.deleteKnowledgeBase(id)
        }

        window.showCreateKnowledgeBaseModal = () => {
            this.showCreateModal()
        }

        window.submitCreateKnowledgeBase = (data) => {
            return this.createKnowledgeBase(data)
        }

        window.submitUpdateKnowledgeBase = (data) => {
            return this.updateKnowledgeBase(this.editingId, data)
        }
    }

    // 显示创建模态框
    showCreateModal() {
        const fields = [
            { name: 'name', label: '知识库名称', type: 'text', required: true, placeholder: '请输入知识库名称' },
            { name: 'description', label: '描述', type: 'textarea', placeholder: '请输入知识库描述' },
            { 
                name: 'chunk_method', 
                label: '分块方法', 
                type: 'select',
                defaultValue: 'naive',
                options: [
                    { value: 'naive', label: '朴素分块' },
                    { value: 'manual', label: '手动分块' },
                    { value: 'qa', label: '问答分块' }
                ]
            }
        ]

        createFormModal('创建知识库', fields, 'submitCreateKnowledgeBase')
    }

    // 创建知识库
    async createKnowledgeBase(data) {
        const result = await knowledgeBaseAPI.create(data)
        showMessage('知识库创建成功', 'success')
        await this.loadData()
        return result
    }

    // 查看知识库详情
    async viewKnowledgeBase(id) {
        try {
            showLoading()
            const kb = await knowledgeBaseAPI.get(id)
            const stats = await knowledgeBaseAPI.getStatistics(id)
            
            const modalHTML = `
                <div class="modal-overlay">
                    <div class="modal" style="width: 800px;">
                        <div class="modal-header">
                            <h3 class="modal-title">知识库详情 - ${escapeHtml(kb.name)}</h3>
                            <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="stats-grid" style="grid-template-columns: repeat(3, 1fr); margin-bottom: 20px;">
                                ${createStatCard('文档数量', stats.total_documents || 0, 'fas fa-file-alt', 'primary')}
                                ${createStatCard('分块数量', stats.total_chunks || 0, 'fas fa-puzzle-piece', 'success')}
                                ${createStatCard('Token数量', stats.total_tokens || 0, 'fas fa-coins', 'warning')}
                            </div>
                            
                            <div class="form-group">
                                <label>知识库ID</label>
                                <input type="text" class="form-control" value="${kb.id}" readonly>
                            </div>
                            <div class="form-group">
                                <label>RAGFlow ID</label>
                                <input type="text" class="form-control" value="${kb.ragflow_kb_id || '-'}" readonly>
                            </div>
                            <div class="form-group">
                                <label>描述</label>
                                <textarea class="form-control" readonly>${kb.description || ''}</textarea>
                            </div>
                            <div class="form-group">
                                <label>分块方法</label>
                                <input type="text" class="form-control" value="${kb.chunk_method}" readonly>
                            </div>
                            <div class="form-group">
                                <label>创建时间</label>
                                <input type="text" class="form-control" value="${formatDate(kb.created_at)}" readonly>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn" onclick="this.closest('.modal-overlay').remove()">关闭</button>
                            <button class="btn btn-primary" onclick="this.closest('.modal-overlay').remove(); editKnowledgeBase('${id}')">编辑</button>
                        </div>
                    </div>
                </div>
            `
            
            document.getElementById('modal-container').innerHTML = modalHTML
        } catch (error) {
            showMessage(error.message || '获取详情失败', 'error')
        } finally {
            hideLoading()
        }
    }

    // 编辑知识库
    async editKnowledgeBase(id) {
        try {
            showLoading()
            const kb = await knowledgeBaseAPI.get(id)
            
            const fields = [
                { name: 'name', label: '知识库名称', type: 'text', required: true },
                { name: 'description', label: '描述', type: 'textarea' },
                { 
                    name: 'chunk_method', 
                    label: '分块方法', 
                    type: 'select',
                    options: [
                        { value: 'naive', label: '朴素分块' },
                        { value: 'manual', label: '手动分块' },
                        { value: 'qa', label: '问答分块' }
                    ]
                },
                {
                    name: 'status',
                    label: '状态',
                    type: 'select',
                    options: [
                        { value: 'active', label: '活跃' },
                        { value: 'inactive', label: '未激活' }
                    ]
                }
            ]

            this.editingId = id
            createFormModal('编辑知识库', fields, 'submitUpdateKnowledgeBase', kb)
        } catch (error) {
            showMessage(error.message || '获取知识库信息失败', 'error')
        } finally {
            hideLoading()
        }
    }

    // 更新知识库
    async updateKnowledgeBase(id, data) {
        const result = await knowledgeBaseAPI.update(id, data)
        showMessage('知识库更新成功', 'success')
        await this.loadData()
        return result
    }

    // 同步知识库
    async syncKnowledgeBase(id) {
        try {
            showLoading()
            await knowledgeBaseAPI.sync(id)
            showMessage('同步成功', 'success')
            await this.loadData()
        } catch (error) {
            showMessage(error.message || '同步失败', 'error')
        } finally {
            hideLoading()
        }
    }

    // 删除知识库
    deleteKnowledgeBase(id) {
        const kb = this.data.find(item => item.id === id)
        if (!kb) return

        showConfirm(
            `确定要删除知识库"${kb.name}"吗？此操作不可恢复。`,
            async () => {
                try {
                    showLoading()
                    await knowledgeBaseAPI.delete(id)
                    showMessage('知识库删除成功', 'success')
                    await this.loadData()
                } catch (error) {
                    showMessage(error.message || '删除失败', 'error')
                } finally {
                    hideLoading()
                }
            }
        )
    }
}
