from app import db
from .base import BaseModel
from werkzeug.security import generate_password_hash, check_password_hash

class User(BaseModel):
    """用户模型"""
    __tablename__ = 'users'
    
    username = db.Column(db.String(80), unique=True, nullable=False, comment='用户名')
    email = db.Column(db.String(120), unique=True, nullable=False, comment='邮箱')
    password_hash = db.Column(db.String(255), nullable=False, comment='密码哈希')
    
    # 用户信息
    nickname = db.Column(db.String(100), comment='昵称')
    avatar = db.Column(db.String(500), comment='头像URL')
    phone = db.Column(db.String(20), comment='手机号')
    
    # 权限和状态
    role = db.Column(db.String(20), default='user', comment='角色: admin, user')
    status = db.Column(db.String(20), default='active', comment='状态: active, inactive, banned')
    
    # 登录信息
    last_login_at = db.Column(db.DateTime, comment='最后登录时间')
    login_count = db.Column(db.Integer, default=0, comment='登录次数')
    
    def __repr__(self):
        return f'<User {self.username}>'
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """转换为字典，排除敏感信息"""
        data = super().to_dict()
        data.pop('password_hash', None)
        return data
    
    @property
    def is_admin(self):
        """是否为管理员"""
        return self.role == 'admin'
