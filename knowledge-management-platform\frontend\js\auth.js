// 认证管理

class AuthManager {
    constructor() {
        this.user = null
        this.token = localStorage.getItem('auth_token')
        this.init()
    }

    init() {
        // 从localStorage恢复用户信息
        const userStr = localStorage.getItem('auth_user')
        if (userStr) {
            try {
                this.user = JSON.parse(userStr)
            } catch (e) {
                console.error('Failed to parse user data:', e)
                this.logout()
            }
        }

        // 设置API token
        if (this.token) {
            api.setToken(this.token)
        }
    }

    // 登录
    async login(username, password) {
        try {
            showLoading()
            const result = await authAPI.login(username, password)
            
            this.token = result.access_token
            this.user = result.user
            
            // 保存到localStorage
            localStorage.setItem('auth_token', this.token)
            localStorage.setItem('auth_user', JSON.stringify(this.user))
            
            // 设置API token
            api.setToken(this.token)
            
            showMessage('登录成功', 'success')
            return true
        } catch (error) {
            showMessage(error.message || '登录失败', 'error')
            return false
        } finally {
            hideLoading()
        }
    }

    // 登出
    logout() {
        this.user = null
        this.token = null
        
        // 清除localStorage
        localStorage.removeItem('auth_token')
        localStorage.removeItem('auth_user')
        
        // 清除API token
        api.setToken(null)
        
        // 跳转到登录页
        this.showLoginPage()
    }

    // 检查是否已登录
    isAuthenticated() {
        return !!(this.token && this.user)
    }

    // 检查是否为管理员
    isAdmin() {
        return this.user && this.user.role === 'admin'
    }

    // 获取当前用户
    getCurrentUser() {
        return this.user
    }

    // 更新用户信息
    updateUser(userData) {
        if (this.user) {
            this.user = { ...this.user, ...userData }
            localStorage.setItem('auth_user', JSON.stringify(this.user))
        }
    }

    // 显示登录页面
    showLoginPage() {
        document.getElementById('login-page').style.display = 'flex'
        document.getElementById('main-app').style.display = 'none'
    }

    // 显示主应用
    showMainApp() {
        document.getElementById('login-page').style.display = 'none'
        document.getElementById('main-app').style.display = 'flex'
        
        // 更新用户信息显示
        if (this.user) {
            document.getElementById('user-nickname').textContent = this.user.nickname || this.user.username
        }
    }

    // 初始化页面显示
    initPageDisplay() {
        if (this.isAuthenticated()) {
            this.showMainApp()
        } else {
            this.showLoginPage()
        }
    }
}

// 创建全局认证管理器
const authManager = new AuthManager()

// 登录表单处理
document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('login-form')
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault()
            
            const formData = new FormData(loginForm)
            const username = formData.get('username')
            const password = formData.get('password')
            
            if (!username || !password) {
                showMessage('请输入用户名和密码', 'error')
                return
            }
            
            const success = await authManager.login(username, password)
            if (success) {
                authManager.showMainApp()
                // 加载默认页面
                if (window.app) {
                    window.app.loadPage('dashboard')
                }
            }
        })
    }

    // 用户菜单处理
    const userMenuBtn = document.getElementById('user-menu-btn')
    const userMenu = document.getElementById('user-menu')
    
    if (userMenuBtn && userMenu) {
        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation()
            userMenu.classList.toggle('show')
        })

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            userMenu.classList.remove('show')
        })
    }

    // 登出按钮
    const logoutBtn = document.getElementById('logout-btn')
    if (logoutBtn) {
        logoutBtn.addEventListener('click', (e) => {
            e.preventDefault()
            showConfirm('确定要退出登录吗？', () => {
                authManager.logout()
            })
        })
    }

    // 个人资料按钮
    const profileBtn = document.getElementById('profile-btn')
    if (profileBtn) {
        profileBtn.addEventListener('click', (e) => {
            e.preventDefault()
            showProfileModal()
        })
    }

    // 初始化页面显示
    authManager.initPageDisplay()
})

// 显示个人资料模态框
function showProfileModal() {
    const user = authManager.getCurrentUser()
    if (!user) return

    const modalHTML = `
        <div class="modal-overlay">
            <div class="modal" style="width: 500px;">
                <div class="modal-header">
                    <h3 class="modal-title">个人资料</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="profile-form">
                        <div class="form-group">
                            <label>用户名</label>
                            <input type="text" class="form-control" value="${user.username}" readonly>
                        </div>
                        <div class="form-group">
                            <label>邮箱</label>
                            <input type="email" class="form-control" value="${user.email}" readonly>
                        </div>
                        <div class="form-group">
                            <label>昵称</label>
                            <input type="text" name="nickname" class="form-control" value="${user.nickname || ''}" placeholder="请输入昵称">
                        </div>
                        <div class="form-group">
                            <label>手机号</label>
                            <input type="tel" name="phone" class="form-control" value="${user.phone || ''}" placeholder="请输入手机号">
                        </div>
                        <div class="form-group">
                            <label>角色</label>
                            <input type="text" class="form-control" value="${user.role === 'admin' ? '管理员' : '普通用户'}" readonly>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="updateProfile()">保存</button>
                </div>
            </div>
        </div>
    `

    document.getElementById('modal-container').innerHTML = modalHTML
}

// 更新个人资料
async function updateProfile() {
    const form = document.getElementById('profile-form')
    const formData = new FormData(form)
    
    const data = {
        nickname: formData.get('nickname'),
        phone: formData.get('phone')
    }

    try {
        showLoading()
        const updatedUser = await authAPI.updateProfile(data)
        authManager.updateUser(updatedUser)
        
        // 更新显示
        document.getElementById('user-nickname').textContent = updatedUser.nickname || updatedUser.username
        
        showMessage('个人资料更新成功', 'success')
        document.querySelector('.modal-overlay').remove()
    } catch (error) {
        showMessage(error.message || '更新失败', 'error')
    } finally {
        hideLoading()
    }
}
