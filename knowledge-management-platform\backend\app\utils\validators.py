import os
import mimetypes
from typing import List, Dict, Any
from werkzeug.datastructures import FileStorage

def validate_json(data: Dict[str, Any], required_fields: List[str]) -> List[str]:
    """验证JSON数据"""
    errors = []
    
    for field in required_fields:
        if field not in data:
            errors.append(f"缺少必需字段: {field}")
        elif data[field] is None or data[field] == '':
            errors.append(f"字段 {field} 不能为空")
    
    return errors

def validate_file(file: FileStorage, allowed_extensions: List[str] = None, max_size: int = None) -> List[str]:
    """验证上传文件"""
    errors = []
    
    if not file or not file.filename:
        errors.append("未选择文件")
        return errors
    
    # 验证文件扩展名
    if allowed_extensions:
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            errors.append(f"不支持的文件类型，支持的类型: {', '.join(allowed_extensions)}")
    
    # 验证文件大小
    if max_size:
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > max_size:
            errors.append(f"文件大小超过限制 ({max_size / (1024*1024):.1f}MB)")
    
    return errors

def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password: str) -> List[str]:
    """验证密码强度"""
    errors = []
    
    if len(password) < 8:
        errors.append("密码长度至少8位")
    
    if not any(c.isupper() for c in password):
        errors.append("密码必须包含大写字母")
    
    if not any(c.islower() for c in password):
        errors.append("密码必须包含小写字母")
    
    if not any(c.isdigit() for c in password):
        errors.append("密码必须包含数字")
    
    return errors

# 支持的文件类型
ALLOWED_DOCUMENT_EXTENSIONS = [
    '.pdf', '.doc', '.docx', '.txt', '.md', '.rtf',
    '.xls', '.xlsx', '.ppt', '.pptx', '.csv'
]

ALLOWED_IMAGE_EXTENSIONS = [
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'
]
