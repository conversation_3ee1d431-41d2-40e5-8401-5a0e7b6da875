from app import db
from .base import BaseModel

class Conversation(BaseModel):
    """对话模型"""
    __tablename__ = 'conversations'
    
    title = db.Column(db.String(255), comment='对话标题')
    assistant_id = db.<PERSON>umn(db.String(36), db.<PERSON>('chat_assistants.id'), nullable=False)
    
    # RAGFlow相关
    ragflow_conversation_id = db.Column(db.String(255), unique=True, comment='RAGFlow对话ID')
    
    # 统计信息
    message_count = db.Column(db.Integer, default=0, comment='消息数量')
    last_message_at = db.Column(db.DateTime, comment='最后消息时间')
    
    # 状态
    status = db.Column(db.String(20), default='active', comment='状态: active, archived')
    
    def __repr__(self):
        return f'<Conversation {self.title}>'

class Message(BaseModel):
    """消息模型"""
    __tablename__ = 'messages'
    
    conversation_id = db.Column(db.String(36), db.<PERSON><PERSON>ey('conversations.id'), nullable=False)
    content = db.Column(db.Text, nullable=False, comment='消息内容')
    role = db.Column(db.String(20), nullable=False, comment='角色: user, assistant')
    
    # 引用信息
    references = db.Column(db.JSON, comment='引用的文档片段')
    
    # 统计信息
    token_count = db.Column(db.Integer, default=0, comment='Token数量')
    response_time = db.Column(db.Float, comment='响应时间(秒)')
    
    # 关联关系
    conversation = db.relationship('Conversation', backref='messages')
    
    def __repr__(self):
        return f'<Message {self.role}: {self.content[:50]}...>'
