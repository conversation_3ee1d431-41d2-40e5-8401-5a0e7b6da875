<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>JavaScript文件加载测试</h1>
    <div id="test-results"></div>

    <!-- 引入JS文件 -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>
    <script src="js/pages/dashboard.js"></script>
    <script src="js/pages/knowledge-base.js"></script>
    <script src="js/pages/chat-assistant.js"></script>
    <script src="js/pages/sensitive-word.js"></script>
    <script src="js/pages/user-management.js"></script>
    <script src="js/app.js"></script>

    <script>
        function runTests() {
            const results = document.getElementById('test-results');
            const tests = [
                { name: 'utils.js', test: () => typeof formatDate === 'function' },
                { name: 'api.js', test: () => typeof api === 'object' && typeof authAPI === 'object' },
                { name: 'auth.js', test: () => typeof authManager === 'object' },
                { name: 'components.js', test: () => typeof createDataTable === 'function' },
                { name: 'app.js', test: () => typeof App === 'function' },
                { name: 'dashboard.js', test: () => typeof DashboardPage === 'function' },
                { name: 'knowledge-base.js', test: () => typeof KnowledgeBasePage === 'function' },
                { name: 'chat-assistant.js', test: () => typeof ChatAssistantPage === 'function' },
                { name: 'sensitive-word.js', test: () => typeof SensitiveWordPage === 'function' },
                { name: 'user-management.js', test: () => typeof UserManagementPage === 'function' }
            ];

            tests.forEach(({ name, test }) => {
                const div = document.createElement('div');
                div.className = 'test-result';
                
                try {
                    if (test()) {
                        div.className += ' success';
                        div.textContent = `✓ ${name} - 加载成功`;
                    } else {
                        div.className += ' error';
                        div.textContent = `✗ ${name} - 测试失败`;
                    }
                } catch (error) {
                    div.className += ' error';
                    div.textContent = `✗ ${name} - 错误: ${error.message}`;
                }
                
                results.appendChild(div);
            });
        }

        // 页面加载完成后运行测试
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
