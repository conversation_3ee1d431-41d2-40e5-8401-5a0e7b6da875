// 主应用控制器

class App {
    constructor() {
        this.currentPage = 'dashboard'
        this.pages = {
            'dashboard': DashboardPage,
            'knowledge-base': KnowledgeBasePage,
            'chat-assistant': <PERSON><PERSON><PERSON>sistantPage,
            'sensitive-word': SensitiveWordPage,
            'user-management': UserManagementPage
        }
        this.init()
    }

    init() {
        // 绑定导航事件
        this.bindNavigation()
        
        // 加载默认页面
        if (authManager.isAuthenticated()) {
            this.loadPage('dashboard')
        }
    }

    // 绑定导航事件
    bindNavigation() {
        const navLinks = document.querySelectorAll('.nav-link')
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault()
                const page = link.getAttribute('data-page')
                this.loadPage(page)
            })
        })
    }

    // 加载页面
    loadPage(pageName) {
        if (!this.pages[pageName]) {
            console.error(`Page ${pageName} not found`)
            return
        }

        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active')
        })
        document.querySelector(`[data-page="${pageName}"]`).classList.add('active')

        // 加载页面内容
        this.currentPage = pageName
        const PageClass = this.pages[pageName]
        const pageInstance = new PageClass()
        pageInstance.render()
    }

    // 获取当前页面
    getCurrentPage() {
        return this.currentPage
    }
}

// 基础页面类
class BasePage {
    constructor() {
        this.container = document.getElementById('page-content')
    }

    render() {
        // 子类需要实现此方法
        throw new Error('render method must be implemented')
    }

    // 渲染页面内容
    renderContent(html) {
        this.container.innerHTML = html
        this.bindEvents()
    }

    // 绑定事件（子类可重写）
    bindEvents() {
        // 默认实现为空
    }

    // 显示加载状态
    showLoading() {
        this.container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-spinner fa-spin"></i>
                <h3>加载中...</h3>
            </div>
        `
    }

    // 显示错误状态
    showError(message) {
        this.container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>加载失败</h3>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="window.app.loadPage('${this.constructor.name.replace('Page', '').toLowerCase().replace(/([A-Z])/g, '-$1').substring(1)}')">重试</button>
            </div>
        `
    }
}

// 页面路由管理
class Router {
    constructor() {
        this.routes = new Map()
        this.currentRoute = null
    }

    // 注册路由
    register(path, handler) {
        this.routes.set(path, handler)
    }

    // 导航到指定路由
    navigate(path) {
        if (this.routes.has(path)) {
            this.currentRoute = path
            const handler = this.routes.get(path)
            handler()
        } else {
            console.error(`Route ${path} not found`)
        }
    }

    // 获取当前路由
    getCurrentRoute() {
        return this.currentRoute
    }
}

// 全局变量
let app
let router

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    // 创建应用实例
    app = new App()
    router = new Router()
    
    // 注册路由
    router.register('dashboard', () => app.loadPage('dashboard'))
    router.register('knowledge-base', () => app.loadPage('knowledge-base'))
    router.register('chat-assistant', () => app.loadPage('chat-assistant'))
    router.register('sensitive-word', () => app.loadPage('sensitive-word'))
    router.register('user-management', () => app.loadPage('user-management'))
    
    console.log('知识库管理平台已初始化')
})
