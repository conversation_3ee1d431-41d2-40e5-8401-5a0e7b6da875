import os
from flask import request, current_app
from werkzeug.utils import secure_filename
from app.api import api_bp
from app.models.document import Document
from app.models.knowledge_base import KnowledgeBase
from app.utils.response import success_response, error_response, paginated_response
from app.utils.decorators import login_required
from app.utils.validators import validate_file, ALLOWED_DOCUMENT_EXTENSIONS
from app.utils.ragflow_client import ragflow_client
import logging

logger = logging.getLogger(__name__)

@api_bp.route('/knowledge-bases/<kb_id>/documents', methods=['GET'])
@login_required
def list_documents(kb_id):
    """获取文档列表"""
    # 验证知识库是否存在
    kb = KnowledgeBase.get_by_id(kb_id)
    if not kb:
        return error_response("知识库不存在", 404)
    
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '').strip()
    status = request.args.get('status', '').strip()
    file_type = request.args.get('file_type', '').strip()
    
    # 构建查询
    query = Document.query.filter_by(knowledge_base_id=kb_id, is_deleted=False)
    
    if search:
        query = query.filter(Document.name.contains(search))
    
    if status:
        query = query.filter_by(parse_status=status)
    
    if file_type:
        query = query.filter_by(file_type=file_type)
    
    # 按创建时间倒序排列
    query = query.order_by(Document.created_at.desc())
    
    # 分页查询
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 转换为字典
    items = [doc.to_dict() for doc in pagination.items]
    
    return paginated_response(
        items=items,
        total=pagination.total,
        page=page,
        per_page=per_page
    )

@api_bp.route('/knowledge-bases/<kb_id>/documents/upload', methods=['POST'])
@login_required
def upload_document(kb_id):
    """上传文档"""
    # 验证知识库是否存在
    kb = KnowledgeBase.get_by_id(kb_id)
    if not kb:
        return error_response("知识库不存在", 404)
    
    if 'file' not in request.files:
        return error_response("未选择文件")
    
    file = request.files['file']
    if not file or not file.filename:
        return error_response("未选择文件")
    
    # 验证文件
    file_errors = validate_file(
        file, 
        allowed_extensions=ALLOWED_DOCUMENT_EXTENSIONS,
        max_size=current_app.config.get('MAX_CONTENT_LENGTH', 100*1024*1024)
    )
    if file_errors:
        return error_response("; ".join(file_errors))
    
    try:
        # 保存文件到本地
        filename = secure_filename(file.filename)
        upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
        os.makedirs(upload_folder, exist_ok=True)
        
        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        file_type = os.path.splitext(filename)[1].lower()
        
        # 上传到RAGFlow
        ragflow_data = ragflow_client.upload_document(
            dataset_id=kb.ragflow_kb_id,
            file_path=file_path,
            name=request.form.get('name', filename),
            parser_config=request.form.get('parser_config', '{}')
        )
        
        # 创建文档记录
        doc = Document(
            name=request.form.get('name', filename),
            original_filename=filename,
            file_type=file_type,
            file_size=file_size,
            file_path=file_path,
            ragflow_doc_id=ragflow_data.get('id'),
            knowledge_base_id=kb_id,
            parse_status='pending'
        )
        doc.save()
        
        # 启动解析
        if doc.ragflow_doc_id:
            try:
                ragflow_client.parse_document(doc.ragflow_doc_id)
                doc.parse_status = 'processing'
                doc.save()
            except Exception as e:
                logger.warning(f"Failed to start parsing: {str(e)}")
        
        logger.info(f"Uploaded document: {filename} to KB {kb.name}")
        return success_response(doc.to_dict(), "文档上传成功")
        
    except Exception as e:
        logger.error(f"Failed to upload document: {str(e)}")
        # 清理本地文件
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        return error_response(f"文档上传失败: {str(e)}")

@api_bp.route('/documents/<doc_id>', methods=['GET'])
@login_required
def get_document(doc_id):
    """获取文档详情"""
    doc = Document.get_by_id(doc_id)
    if not doc:
        return error_response("文档不存在", 404)
    
    try:
        # 从RAGFlow获取最新状态
        if doc.ragflow_doc_id:
            status_data = ragflow_client.get_document_status(doc.ragflow_doc_id)
            doc.parse_status = status_data.get('status', doc.parse_status)
            doc.parse_progress = status_data.get('progress', doc.parse_progress)
            doc.chunk_count = status_data.get('chunk_count', doc.chunk_count)
            doc.token_count = status_data.get('token_count', doc.token_count)
            doc.save()
        
        return success_response(doc.to_dict())
        
    except Exception as e:
        logger.warning(f"Failed to sync document status: {str(e)}")
        return success_response(doc.to_dict())

@api_bp.route('/documents/<doc_id>', methods=['DELETE'])
@login_required
def delete_document(doc_id):
    """删除文档"""
    doc = Document.get_by_id(doc_id)
    if not doc:
        return error_response("文档不存在", 404)
    
    try:
        # 从RAGFlow中删除文档
        if doc.ragflow_doc_id:
            ragflow_client.delete_document(doc.ragflow_doc_id)
        
        # 删除本地文件
        if doc.file_path and os.path.exists(doc.file_path):
            os.remove(doc.file_path)
        
        # 软删除记录
        doc.delete()
        
        logger.info(f"Deleted document: {doc.name} (ID: {doc.id})")
        return success_response(message="文档删除成功")
        
    except Exception as e:
        logger.error(f"Failed to delete document: {str(e)}")
        return error_response(f"删除文档失败: {str(e)}")

@api_bp.route('/documents/<doc_id>/reparse', methods=['POST'])
@login_required
def reparse_document(doc_id):
    """重新解析文档"""
    doc = Document.get_by_id(doc_id)
    if not doc:
        return error_response("文档不存在", 404)
    
    if not doc.ragflow_doc_id:
        return error_response("文档未关联RAGFlow")
    
    try:
        # 重新解析
        ragflow_client.parse_document(doc.ragflow_doc_id)
        
        # 更新状态
        doc.parse_status = 'processing'
        doc.parse_progress = 0.0
        doc.parse_error = None
        doc.save()
        
        logger.info(f"Reparse document: {doc.name} (ID: {doc.id})")
        return success_response(doc.to_dict(), "重新解析已启动")
        
    except Exception as e:
        logger.error(f"Failed to reparse document: {str(e)}")
        return error_response(f"重新解析失败: {str(e)}")

@api_bp.route('/documents/<doc_id>/chunks', methods=['GET'])
@login_required
def get_document_chunks(doc_id):
    """获取文档分块"""
    doc = Document.get_by_id(doc_id)
    if not doc:
        return error_response("文档不存在", 404)
    
    if not doc.ragflow_doc_id:
        return error_response("文档未关联RAGFlow")
    
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        # 从RAGFlow获取分块数据
        chunks_data = ragflow_client.list_documents(
            dataset_id=doc.knowledge_base.ragflow_kb_id,
            page=page,
            per_page=per_page
        )
        
        return success_response(chunks_data)
        
    except Exception as e:
        logger.error(f"Failed to get document chunks: {str(e)}")
        return error_response(f"获取文档分块失败: {str(e)}")
