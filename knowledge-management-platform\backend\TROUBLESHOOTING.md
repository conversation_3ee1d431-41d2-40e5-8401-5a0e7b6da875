# 后端故障排除指南

## SQLAlchemy兼容性问题

### 问题描述
```
AssertionError: Class <class 'sqlalchemy.sql.elements.SQLCoreOperations'> directly inherits TypingOnly but has additional attributes
```

### 原因
Python 3.13与SQLAlchemy 2.0+版本存在兼容性问题。

### 解决方案

#### 方案一：使用兼容版本（推荐）
```bash
# 使用Python 3.13兼容的依赖包
pip install -r requirements-py313.txt
```

#### 方案二：自动修复
```bash
# 运行兼容性检查和自动修复
python check_compatibility.py

# 或使用智能安装脚本
python install_deps.py
```

#### 方案三：手动安装兼容版本
```bash
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==2.5.1
pip install SQLAlchemy==1.4.53
pip install Werkzeug==2.3.7
```

#### 方案四：降级Python版本
建议使用Python 3.8-3.12版本：
```bash
# 使用pyenv安装Python 3.11
pyenv install 3.11.6
pyenv local 3.11.6

# 或使用conda
conda create -n knowledge-platform python=3.11
conda activate knowledge-platform
```

## 其他常见问题

### 1. 模块导入错误
```
ModuleNotFoundError: No module named 'flask'
```

**解决方案:**
```bash
# 安装依赖
pip install -r requirements.txt

# 或使用智能安装
python install_deps.py
```

### 2. 数据库连接错误
```
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server")
```

**解决方案:**
1. 确保MySQL服务正在运行
2. 检查数据库配置
3. 创建数据库：
   ```sql
   CREATE DATABASE knowledge_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

### 3. 端口占用错误
```
OSError: [Errno 48] Address already in use
```

**解决方案:**
```bash
# 查找占用端口的进程
netstat -tulpn | grep :5000

# 杀死进程
kill -9 <PID>

# 或修改端口
export FLASK_RUN_PORT=5001
```

### 4. 权限错误
```
PermissionError: [Errno 13] Permission denied
```

**解决方案:**
```bash
# Linux/macOS
chmod +x start.sh
sudo chown -R $USER:$USER .

# Windows
# 以管理员身份运行命令提示符
```

### 5. 虚拟环境问题
```
ImportError: No module named 'venv'
```

**解决方案:**
```bash
# 安装venv模块
python -m pip install virtualenv

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

## 环境配置检查清单

### Python环境
- [ ] Python版本 3.8-3.12 (推荐)
- [ ] pip版本最新
- [ ] 虚拟环境已创建并激活

### 依赖包
- [ ] Flask及相关扩展
- [ ] SQLAlchemy (兼容版本)
- [ ] PyMySQL
- [ ] Redis客户端

### 数据库
- [ ] MySQL服务运行中
- [ ] 数据库已创建
- [ ] 连接配置正确

### 配置文件
- [ ] .env文件存在
- [ ] 环境变量配置正确
- [ ] 密钥和API Key配置

## 快速诊断命令

### 检查Python环境
```bash
python --version
pip --version
python -c "import sys; print(sys.executable)"
```

### 检查依赖包
```bash
pip list | grep -i flask
pip list | grep -i sqlalchemy
python -c "import flask, sqlalchemy; print('OK')"
```

### 检查数据库连接
```bash
python -c "
import pymysql
try:
    conn = pymysql.connect(host='localhost', user='root', password='your_password')
    print('MySQL连接成功')
    conn.close()
except Exception as e:
    print(f'MySQL连接失败: {e}')
"
```

### 检查端口占用
```bash
# Linux/macOS
lsof -i :5000

# Windows
netstat -ano | findstr :5000
```

## 完整重置步骤

如果遇到无法解决的问题，可以尝试完整重置：

```bash
# 1. 删除虚拟环境
rm -rf venv

# 2. 创建新的虚拟环境
python -m venv venv

# 3. 激活虚拟环境
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 4. 升级pip
python -m pip install --upgrade pip

# 5. 安装兼容依赖
python install_deps.py

# 6. 验证安装
python check_compatibility.py

# 7. 启动应用
python run.py
```

## 获取帮助

如果以上方案都无法解决问题，请：

1. 收集错误信息：
   - 完整的错误堆栈
   - Python版本信息
   - 操作系统信息
   - 已安装的包列表

2. 检查日志文件：
   - 应用日志
   - 系统日志
   - 数据库日志

3. 提供环境信息：
   ```bash
   python --version
   pip list
   uname -a  # Linux/macOS
   systeminfo  # Windows
   ```
