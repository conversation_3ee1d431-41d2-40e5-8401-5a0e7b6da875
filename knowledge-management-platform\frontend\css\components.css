/* 表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.data-table th,
.data-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.data-table th {
    background-color: #fafafa;
    font-weight: 600;
    color: #333;
}

.data-table tbody tr:hover {
    background-color: #f5f5f5;
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
}

.pagination-info {
    color: #666;
    font-size: 14px;
    margin-right: 16px;
}

.pagination-btn {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    background: white;
    color: #333;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.pagination-btn:hover:not(:disabled) {
    border-color: #1890ff;
    color: #1890ff;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: #1890ff;
    border-color: #1890ff;
    color: white;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
    padding: 4px;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 24px;
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-icon.primary {
    background: #1890ff;
}

.stat-icon.success {
    background: #52c41a;
}

.stat-icon.warning {
    background: #fa8c16;
}

.stat-icon.danger {
    background: #ff4d4f;
}

.stat-content h3 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.stat-content p {
    color: #666;
    font-size: 14px;
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 8px;
}

/* 文件上传区域 */
.upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background: #fafafa;
    transition: border-color 0.3s;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #1890ff;
}

.upload-area.dragover {
    border-color: #1890ff;
    background: #e6f7ff;
}

.upload-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
}

.upload-text {
    color: #666;
    font-size: 14px;
}

.upload-hint {
    color: #999;
    font-size: 12px;
    margin-top: 8px;
}

/* 进度条 */
.progress {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: #1890ff;
    transition: width 0.3s;
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid;
}

.tag.primary {
    background: #e6f7ff;
    color: #1890ff;
    border-color: #91d5ff;
}

.tag.success {
    background: #f6ffed;
    color: #52c41a;
    border-color: #b7eb8f;
}

.tag.warning {
    background: #fff2e8;
    color: #fa8c16;
    border-color: #ffd591;
}

.tag.danger {
    background: #fff2f0;
    color: #ff4d4f;
    border-color: #ffccc7;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d9d9d9;
}

.empty-state h3 {
    font-size: 16px;
    margin-bottom: 8px;
    color: #666;
}

.empty-state p {
    font-size: 14px;
    color: #999;
}

/* 消息提示 */
.message {
    position: fixed;
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    z-index: 9999;
    animation: slideDown 0.3s ease;
}

.message.success {
    background: #52c41a;
}

.message.error {
    background: #ff4d4f;
}

.message.warning {
    background: #fa8c16;
}

.message.info {
    background: #1890ff;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 响应式表格 */
@media (max-width: 768px) {
    .data-table {
        font-size: 12px;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px 12px;
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
